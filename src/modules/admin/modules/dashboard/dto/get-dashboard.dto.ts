import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsNumber, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';
import { BaseLogQueryDto } from '../../../../../common/dto/base-log-query.dto';

export class GetDashboardDto extends BaseLogQueryDto {
  @ApiPropertyOptional({
    description: 'Role ID to filter data',
    example: '20',
  })
  @IsOptional()
  @Transform(({ value }) => (value ? +value : undefined))
  @IsNumber({}, { message: 'Role ID must be a number' })
  role_id?: number;

  @ApiPropertyOptional({
    description: 'User ID to filter data',
    example: '29',
  })
  @IsOptional()
  @Transform(({ value }) => (value ? +value : undefined))
  @IsNumber({}, { message: 'User ID must be a number' })
  user_id?: number;

  @ApiPropertyOptional({
    description: 'Activity ID to filter activity logs',
    example: '1',
  })
  @IsOptional()
  @Transform(({ value }) => (value ? +value : undefined))
  @IsNumber({}, { message: 'Activity ID must be a number' })
  activity_id?: number;

  @ApiPropertyOptional({
    description: 'Checkpoint ID to filter checkpoint logs',
    example: '1',
  })
  @IsOptional()
  @Transform(({ value }) => (value ? +value : undefined))
  @IsNumber({}, { message: 'Checkpoint ID must be a number' })
  checkpoint_id?: number;

  @ApiPropertyOptional({
    description: 'Zone ID to filter checkpoint logs',
    example: '1',
  })
  @IsOptional()
  @Transform(({ value }) => (value ? +value : undefined))
  @IsNumber({}, { message: 'Zone ID must be a number' })
  zone_id?: number;

  @ApiPropertyOptional({
    description: 'Form ID to filter form logs',
    example: '1',
  })
  @IsOptional()
  @Transform(({ value }) => (value ? +value : undefined))
  @IsNumber({}, { message: 'Form ID must be a number' })
  form_id?: number;

  @ApiPropertyOptional({
    description: 'Task ID to filter task logs',
    example: '1',
  })
  @IsOptional()
  @Transform(({ value }) => (value ? +value : undefined))
  @IsNumber({}, { message: 'Task ID must be a number' })
  task_id?: number;

  @ApiPropertyOptional({
    description: 'Include sign in/out data (returns all data without pagination)',
    example: 'true',
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean({ message: 'signonoff must be a boolean' })
  signonoff?: boolean;

  @ApiPropertyOptional({
    description: 'Include activity data (returns all data without pagination)',
    example: 'true',
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean({ message: 'activity must be a boolean' })
  activity?: boolean;

  @ApiPropertyOptional({
    description: 'Include alarm data (returns all data without pagination)',
    example: 'true',
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean({ message: 'alarm must be a boolean' })
  alarm?: boolean;

  @ApiPropertyOptional({
    description: 'Include checkpoint data (returns all data without pagination)',
    example: 'true',
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean({ message: 'checkpoint must be a boolean' })
  checkpoint?: boolean;

  @ApiPropertyOptional({
    description: 'Include form data (returns all data without pagination)',
    example: 'true',
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean({ message: 'form must be a boolean' })
  form?: boolean;

  @ApiPropertyOptional({
    description: 'Include task data (returns all data without pagination)',
    example: 'true',
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean({ message: 'task must be a boolean' })
  task?: boolean;

  @ApiPropertyOptional({
    description: 'Include geofence data (returns all data without pagination)',
    example: 'true',
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean({ message: 'geofence must be a boolean' })
  geofence?: boolean;
} 