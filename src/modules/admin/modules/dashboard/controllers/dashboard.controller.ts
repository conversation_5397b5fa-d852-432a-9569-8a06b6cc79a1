import { Get, Query } from '@nestjs/common';
import { WebAdminController } from '../../../../../common/decorators/api-controller.decorator';
import { DashboardService } from '../services/dashboard.service';
import { CurrentBranch } from '../../../../../common/decorators/current-branch.decorator';
import { Branch } from '../../../../../common/modules/database/entities/branch.entity';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { GetDashboardDto } from '../dto/get-dashboard.dto';
import { CurrentUser } from '../../../../../common/decorators/current-user.decorator';
import { User } from '../../../../../common/modules/database/entities/user.entity';

@WebAdminController()
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Get()
  @ApiOperation({ 
    summary: 'Get dashboard summary data',
    description: 'Returns dashboard data including active users and optional sign in/out, activity, alarm, checkpoint, form, task, and geofence logs (all data without pagination). Response includes branch timezone information in meta.'
  })
  @ApiResponse({
    status: 200,
    description:
      'Returns dashboard data including active users and optional sign in/out, activity, alarm, checkpoint, form, task, and geofence logs (all records without pagination). Meta includes branch timezone information.',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            active_users: { type: 'array', description: 'Currently active users with valid tokens' },
            sign_in_out: { type: 'array', description: 'Sign in/out logs (if requested)' },
            activity: { type: 'array', description: 'Activity logs (if requested)' },
            alarm: { type: 'array', description: 'Alarm logs (if requested)' },
            checkpoint: { type: 'array', description: 'Checkpoint logs (if requested)' },
            form: { type: 'array', description: 'Form logs (if requested)' },
            task: { type: 'array', description: 'Task logs (if requested)' },
            geofence: { type: 'array', description: 'Geofence logs (if requested)' },
          }
        },
        meta: {
          type: 'object',
          properties: {
            timestamp: { type: 'string', description: 'Response timestamp (ISO)' },
            local_time: { type: 'string', description: 'Response local time' },
            branch_timezone_id: { type: 'number', description: 'Branch timezone ID' },
            branch_timezone_name: { type: 'string', description: 'Branch timezone name' },
            branch_timezone_gmt_offset: { type: 'string', description: 'Branch timezone GMT offset' },
          }
        }
      }
    }
  })
  async getDashboardData(
    @Query() query: GetDashboardDto,
    @CurrentBranch() branch: Branch,
    @CurrentUser() user: User,
  ) {
    return this.dashboardService.getDashboardData(branch, user, query);
  }
}
