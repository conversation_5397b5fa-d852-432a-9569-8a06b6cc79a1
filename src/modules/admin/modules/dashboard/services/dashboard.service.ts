import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Brackets, MoreThan } from 'typeorm';
import { Branch } from '../../../../../common/modules/database/entities/branch.entity';
import { User } from '../../../../../common/modules/database/entities/user.entity';
import { GetDashboardDto } from '../dto/get-dashboard.dto';
import { SignInOutAnalyticService } from '../../../../../common/modules/analytics/sign-in-out/services/sign-in-out-analytic.service';
import { ActivityLogAnalyticService } from '../../../../../common/modules/analytics/activity/services/activity-log-analytic.service';
import { AlarmAnalyticService } from '../../../../../common/modules/analytics/alarm/services/alarm-analytic.service';
import { CheckpointActivityAnalyticService } from '../../../../../common/modules/analytics/checkpoint-activity/services/checkpoint-activity-analytic.service';
import { FormAnalyticService } from '../../../../../common/modules/analytics/form/services/form-analytic.service';
import { TaskAnalyticService } from '../../../../../common/modules/analytics/task/services/task-analytic.service';
import { GeofenceAnalyticService } from '../../../../../common/modules/analytics/geofence/services/geofence-analytic.service';
import { Timezone } from '../../../../../common/modules/database/entities/timezone.entity';
import { LogGeolocation } from '../../../../../common/modules/database/entities/log-geolocation.entity';
import { AuthAccessToken } from '../../../../../common/modules/database/entities/auth-access-token.entity';
import { parseTimeDaily } from '../../../../../common/utils/time-utils';
import { OrderDirection } from '../../../../../common/dto/base-query.dto';

@Injectable()
export class DashboardService {
  constructor(
    @InjectRepository(Timezone)
    private readonly timezoneRepository: Repository<Timezone>,
    @InjectRepository(LogGeolocation)
    private readonly logGeolocationRepository: Repository<LogGeolocation>,
    @InjectRepository(AuthAccessToken)
    private readonly authAccessTokenRepository: Repository<AuthAccessToken>,
    private readonly signInOutAnalyticService: SignInOutAnalyticService,
    private readonly activityLogAnalyticService: ActivityLogAnalyticService,
    private readonly alarmAnalyticService: AlarmAnalyticService,
    private readonly checkpointActivityAnalyticService: CheckpointActivityAnalyticService,
    private readonly formAnalyticService: FormAnalyticService,
    private readonly taskAnalyticService: TaskAnalyticService,
    private readonly geofenceAnalyticService: GeofenceAnalyticService,
  ) { }

  async getDashboardData(branch: Branch, user: User, query: GetDashboardDto) {
    const result: any = {
      data: {},
      meta: {},
    };

    const timezone = await this.timezoneRepository.findOne({ where: { id: branch.timezone_id } });
    if (!timezone) {
      throw new NotFoundException('Timezone not found');
    }

    // Get active users (tokens that haven't expired) - grouped by user_id
    const activeTokens = await this.authAccessTokenRepository
      .createQueryBuilder('token')
      .leftJoinAndSelect('token.user', 'user')
      .where('token.expires_at > :now', { now: new Date() })
      .andWhere('user.parent_branch_id = :parentBranchId', { parentBranchId: branch.parent_id })
      .orderBy('token.last_used_at', 'DESC')
      .getMany();

    // Group by user_id and get the most recent token for each user
    const groupedActiveUsers = activeTokens.reduce((acc, token) => {
      const userId = token.user?.id;
      if (userId && !acc[userId]) {
        acc[userId] = token;
      }
      return acc;
    }, {} as Record<number, any>);

    const mappedActiveUsers = Object.values(groupedActiveUsers).map((token: any) => ({
      token_id: token.id,
      user_id: token.user?.id || null,
      user_name: token.user?.name || null,
      device_name: token.device_name || null,
      device_uid: token.device_uid || null,
      user_agent: token.user_agent || null,
      last_used_at: token.last_used_at,
      expires_at: token.expires_at,
      created_at: token.created_at,
    }));

    result.data.active_users = mappedActiveUsers;

    // Create array of promises for parallel execution
    const promises: Promise<any>[] = [];
    const promiseKeys: string[] = [];

    if (query.signonoff) {
      promises.push(
        this.signInOutAnalyticService.getSignInOut(
          branch.parent_id,
          {
            startDate: query.start_date,
            endDate: query.end_date,
            startTime: query.start_time || '00:00:00',
            endTime: query.end_time || '23:59:59',
            branchId: query.branch_id,
            userId: query.user_id,
            userLabels: undefined,
            deviceId: undefined,
            deviceLabels: undefined,
            limit: 999999,
            page: 1,
            orderBy: query.order_by || 'event_time',
            orderDirection: query.order_direction,
          },
          {
            timezone,
            user,
          },
        )
      );
      promiseKeys.push('signonoff');
    }

    if (query.activity) {
      promises.push(
        this.activityLogAnalyticService.getActivityLogs(
          branch.parent_id,
          {
            startDate: query.start_date,
            endDate: query.end_date,
            startTime: query.start_time || '00:00:00',
            endTime: query.end_time || '23:59:59',
            branchId: query.branch_id,
            roleId: query.role_id,
            userId: query.user_id,
            userLabels: undefined,
            deviceId: undefined,
            deviceLabels: undefined,
            activityId: query.activity_id,
            limit: 999999,
            page: 1,
            orderBy: query.order_by || 'original_submitted_time',
            orderDirection: query.order_direction,
          },
          {
            timezone,
            user,
          },
        )
      );
      promiseKeys.push('activity');
    }

    if (query.alarm) {
      promises.push(
        this.alarmAnalyticService.getAlarmLogs(
          branch.parent_id,
          {
            startDate: query.start_date,
            endDate: query.end_date,
            startTime: query.start_time || '00:00:00',
            endTime: query.end_time || '23:59:59',
            branchId: query.branch_id,
            roleId: query.role_id,
            userId: query.user_id,
            userLabels: undefined,
            deviceId: undefined,
            deviceLabels: undefined,
            limit: 999999,
            page: 1,
            orderBy: query.order_by || 'original_submitted_time',
            orderDirection: query.order_direction,
          },
          {
            timezone,
          },
        )
      );
      promiseKeys.push('alarm');
    }

    if (query.checkpoint) {
      promises.push(
        this.checkpointActivityAnalyticService.getCheckpointActivity(
          branch.parent_id,
          {
            startDate: query.start_date,
            endDate: query.end_date,
            startTime: query.start_time || '00:00:00',
            endTime: query.end_time || '23:59:59',
            branchId: query.branch_id,
            roleId: query.role_id,
            userId: query.user_id,
            userLabels: undefined,
            deviceId: undefined,
            deviceLabels: undefined,
            zoneId: query.zone_id,
            zoneLabels: undefined,
            checkpointId: query.checkpoint_id,
            checkpointLabels: undefined,
            limit: 999999,
            page: 1,
            orderBy: query.order_by || 'original_submitted_time',
            orderDirection: query.order_direction,
          },
          {
            timezone,
            user,
          },
        )
      );
      promiseKeys.push('checkpoint');
    }

    if (query.form) {
      promises.push(
        this.formAnalyticService.getFormLogs(
          branch.parent_id,
          {
            startDate: query.start_date,
            endDate: query.end_date,
            startTime: query.start_time || '00:00:00',
            endTime: query.end_time || '23:59:59',
            branchId: query.branch_id,
            roleId: query.role_id,
            userId: query.user_id,
            userLabels: undefined,
            deviceId: undefined,
            deviceLabels: undefined,
            formId: query.form_id,
            limit: 999999,
            page: 1,
            orderBy: query.order_by || 'original_submitted_time',
            orderDirection: query.order_direction,
          },
          {
            timezone,
            user,
          },
        )
      );
      promiseKeys.push('form');
    }

    if (query.task) {
      promises.push(
        this.taskAnalyticService.getTasks(
          branch.parent_id,
          {
            startDate: query.start_date,
            endDate: query.end_date,
            startTime: query.start_time || '00:00:00',
            endTime: query.end_time || '23:59:59',
            branchId: query.branch_id,
            roleId: query.role_id,
            userId: query.user_id,
            userLabels: undefined,
            deviceId: undefined,
            deviceLabels: undefined,
            taskId: query.task_id,
            limit: 999999,
            page: 1,
            orderBy: query.order_by || 'original_submitted_time',
            orderDirection: query.order_direction,
          },
          {
            timezone,
            user,
          },
        )
      );
      promiseKeys.push('task');
    }

    if (query.geofence) {
      // Create geofence query promise
      const geofenceQueryBuilder = this.logGeolocationRepository
        .createQueryBuilder('log_geolocation')
        .where('log_geolocation.parent_branch_id = :parentBranchId', {
          parentBranchId: branch.parent_id,
        })
        .andWhere('log_geolocation.geofence_id IS NOT NULL');

      // Apply date/time filters with timezone adjustment
      const rangeDateAndTimeDaily = parseTimeDaily({
        startDate: query.start_date,
        endDate: query.end_date,
        startTime: query.start_time || '00:00:00',
        endTime: query.end_time || '23:59:59',
        offset: timezone.gmt_offset,
      });

      if (rangeDateAndTimeDaily.length > 0) {
        geofenceQueryBuilder.andWhere(
          new Brackets(qb => {
            for (let i = 0; i < rangeDateAndTimeDaily.length; i++) {
              qb.orWhere(
                `log_geolocation.original_submitted_time >= :startDate${i}::timestamptz AND log_geolocation.original_submitted_time <= :endDate${i}::timestamptz`,
                {
                  [`startDate${i}`]: rangeDateAndTimeDaily[i].utc.startDate + '+00',
                  [`endDate${i}`]: rangeDateAndTimeDaily[i].utc.endDate + '+00',
                },
              );
            }
          }),
        );
      }

      // Apply filters
      if (query.branch_id) {
        geofenceQueryBuilder.andWhere('log_geolocation.branch_id = :branchId', {
          branchId: query.branch_id,
        });
      }

      if (query.role_id) {
        geofenceQueryBuilder.andWhere('log_geolocation.role_id = :roleId', {
          roleId: query.role_id,
        });
      }

      if (query.user_id) {
        geofenceQueryBuilder.andWhere('log_geolocation.user_id = :userId', {
          userId: query.user_id,
        });
      }

      if (query.zone_id) {
        geofenceQueryBuilder.andWhere('log_geolocation.zone_id = :zoneId', {
          zoneId: query.zone_id,
        });
      }

      // Apply sorting
      const orderBy = query.order_by || 'original_submitted_time';
      const orderDirection = query.order_direction || OrderDirection.DESC;
      geofenceQueryBuilder.orderBy(`log_geolocation.${orderBy}`, orderDirection);

      // Apply limit (no pagination for dashboard)
      geofenceQueryBuilder.limit(999999);

      promises.push(geofenceQueryBuilder.getMany());
      promiseKeys.push('geofence');
    }

    // Execute all promises in parallel
    const results = await Promise.all(promises);

    // Process results and map data
    results.forEach((data, index) => {
      const key = promiseKeys[index];

      switch (key) {
        case 'signonoff':
          const mappedSignInOutData = data.data.map((item: any) => ({
            uuid: item.uuid,
            user_log_type: item.user_log_type,
            user_name: item.user_name,
            device_name: item.device_name,
            timezone_id: item.timezone_id,
            timezone_name: item.timezone_name,
            latitude: item.latitude,
            longitude: item.longitude,
            event_time: item.event_time,
          }));
          result.data.sign_in_out = mappedSignInOutData;
          break;

        case 'activity':
          const mappedActivityData = data.data.map((item: any) => ({
            uuid: item.uuid,
            activity_name: item.activity?.activity_name || null,
            user_name: item.user?.name || null,
            device_name: item.device?.device_name || null,
            timezone_id: item.timezone?.id || null,
            timezone_name: item.timezone?.timezone_name || null,
            latitude: item.latitude,
            longitude: item.longitude,
            original_submitted_time: item.original_submitted_time,
          }));
          result.data.activity = mappedActivityData;
          break;

        case 'alarm':
          const mappedAlarmData = data.data.map((item: any) => ({
            uuid: item.uuid,
            alarm_type: item.alarm_type || null,
            alarm_message: item.alarm_message || null,
            user_name: item.user?.name || null,
            device_name: item.device?.device_name || null,
            timezone_id: item.timezone?.id || null,
            timezone_name: item.timezone?.timezone_name || null,
            latitude: item.start_latitude,
            longitude: item.start_longitude,
            original_submitted_time: item.original_submitted_time,
          }));
          result.data.alarm = mappedAlarmData;
          break;

        case 'checkpoint':
          const mappedCheckpointData = data.data.map((item: any) => ({
            uuid: item.uuid,
            checkpoint_name: item.checkpoint?.checkpoint_name || null,
            zone_name: item.zone?.zone_name || null,
            user_name: item.user?.name || null,
            device_name: item.device?.device_name || null,
            timezone_id: item.timezone?.id || null,
            timezone_name: item.timezone?.timezone_name || null,
            latitude: item.latitude,
            longitude: item.longitude,
            original_submitted_time: item.original_submitted_time,
          }));
          result.data.checkpoint = mappedCheckpointData;
          break;

        case 'form':
          const mappedFormData = data.data.map((item: any) => ({
            uuid: item.uuid,
            form_name: item.form?.form_name || null,
            user_name: item.user?.name || null,
            device_name: item.device?.device_name || null,
            timezone_id: item.timezone?.id || null,
            timezone_name: item.timezone?.timezone_name || null,
            latitude: item.latitude,
            longitude: item.longitude,
            original_submitted_time: item.original_submitted_time,
          }));
          result.data.form = mappedFormData;
          break;

        case 'task':
          const mappedTaskData = data.data.map((item: any) => ({
            uuid: item.uuid,
            task_name: item.task?.task_name || null,
            user_name: item.user?.name || null,
            device_name: item.device?.device_name || null,
            timezone_id: item.timezone?.id || null,
            timezone_name: item.timezone?.timezone_name || null,
            latitude: item.latitude,
            longitude: item.longitude,
            original_submitted_time: item.original_submitted_time,
          }));
          result.data.task = mappedTaskData;
          break;

        case 'geofence':
          const mappedGeofenceData = data.map((item: any) => ({
            uuid: item.uuid,
            geofence_name: item.geofence_name || null,
            zone_name: item.zone_name || null,
            user_name: item.user_name || null,
            device_name: item.device_name || null,
            timezone_id: item.timezone_id || null,
            timezone_name: item.timezone_name || null,
            latitude: item.latitude,
            longitude: item.longitude,
            original_submitted_time: item.original_submitted_time,
          }));
          result.data.geofence = mappedGeofenceData;
          break;
      }
    });

    // Add meta information with branch timezone
    result.meta = {
      branch_timezone_id: timezone.id,
      branch_timezone_name: timezone.timezone_name,
      branch_timezone_gmt_offset: timezone.gmt_offset,
    };

    return result;
  }
}
