import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { Alert } from '../../../../../common/modules/database/entities/alert.entity';
import { AlertCondition } from '../../../../../common/modules/database/entities/alert-condition.entity';
import { AlertBranch } from '../../../../../common/modules/database/entities/alert-branch.entity';
import { AlertRecipient } from '../../../../../common/modules/database/entities/alert-recipient.entity';
import { CreateAlertDto } from '../dto/create-alert.dto';
import { UpdateAlertDto } from '../dto/update-alert.dto';
import { GetAlertsDto } from '../dto/get-alerts.dto';
import { EConditionType } from '../../../../../common/modules/database/entities/alert-condition-type.entity';
import { Branch } from '../../../../../common/modules/database/entities/branch.entity';
import { User } from '../../../../../common/modules/database/entities/user.entity';

@Injectable()
export class AlertService {
  constructor(
    @InjectRepository(Alert)
    private alertRepository: Repository<Alert>,
    @InjectRepository(AlertCondition)
    private alertConditionRepository: Repository<AlertCondition>,
    @InjectRepository(AlertBranch)
    private alertBranchRepository: Repository<AlertBranch>,
    @InjectRepository(AlertRecipient)
    private alertRecipientRepository: Repository<AlertRecipient>,
    private dataSource: DataSource,
  ) {}

  async getAlerts(query: GetAlertsDto, user: User, branch: Branch) {
    const {
      search,
      limit,
      page = 1,
      order_by = 'created_at',
      order_direction = 'DESC',
      ignore_active_status,
    } = query;

    const queryBuilder = this.alertRepository
      .createQueryBuilder('alert')
      .leftJoinAndSelect('alert.parent_branch', 'parent_branch')
      .leftJoinAndSelect('alert.alert_action', 'alert_action')
      .leftJoinAndSelect('alert.alert_event', 'alert_event')
      .leftJoinAndSelect('alert.conditions', 'conditions')
      .leftJoinAndSelect(
        'conditions.alert_condition_type',
        'alert_condition_type',
      )
      .leftJoinAndSelect('conditions.user', 'user')
      .leftJoinAndSelect('conditions.role', 'role')
      .leftJoinAndSelect('conditions.checkpoint', 'checkpoint')
      .leftJoinAndSelect('conditions.geofence', 'geofence')
      .leftJoinAndSelect('conditions.device', 'device')
      .leftJoinAndSelect('alert.recipients', 'recipients')
      .leftJoinAndSelect('alert.branches', 'branches')
      .where('alert.parent_branch_id = :parentBranchId', {
        parentBranchId: branch.parent_id,
      })
      .innerJoin('alert_branches', 'ab', 'ab.alert_id = alert.id')
      .where('ab.branch_id = :branchId', { branchId: branch.id });

    if (search) {
      queryBuilder.andWhere(
        '(alert.alert_name ILIKE :search OR alert.alert_description ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    if (!ignore_active_status) {
      queryBuilder.andWhere('alert.active = :active', { active: true });
      queryBuilder.andWhere('alert_branches.active = :branchActive', {
        branchActive: true,
      });
    }

    queryBuilder.orderBy(`alert.${order_by}`, order_direction);

    const total = await queryBuilder.getCount();

    if (limit && page) {
      queryBuilder.skip((page - 1) * limit).take(limit);
    }

    const alerts = await queryBuilder.getMany();

    return {
      data: alerts,
      meta: {
        total,
        page,
        limit,
      },
    };
  }

  async getAlertById(id: number, user: User, branch: Branch) {
    const alert = await this.alertRepository
      .createQueryBuilder('alert')
      .leftJoinAndSelect('alert.parent_branch', 'parent_branch')
      .leftJoinAndSelect('alert.alert_action', 'alert_action')
      .leftJoinAndSelect('alert.alert_event', 'alert_event')
      .leftJoinAndSelect('alert.conditions', 'conditions')
      .leftJoinAndSelect('alert.alert_branches', 'alert_branches')
      .leftJoinAndSelect('alert.recipients', 'recipients')
      // Join dengan user_branches untuk filter berdasarkan akses user
      .innerJoin(
        'user_branches',
        'ub',
        'ub.branch_id = alert_branches.branch_id',
      )
      .where('alert.id = :id', { id })
      .andWhere('ub.user_id = :userId', { userId: user.id })
      .andWhere('ub.active = :userBranchActive', { userBranchActive: true })
      .andWhere('alert_branches.active = :alertBranchActive', {
        alertBranchActive: true,
      })
      .getOne();

    if (!alert) {
      throw new Error('Alert not found');
    }

    return alert;
  }

  async createAlert(
    createAlertDto: CreateAlertDto,
    user: User,
    branch: Branch,
  ) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Create alert using queryBuilder
      const alertInsertResult = await queryRunner.manager
        .createQueryBuilder()
        .insert()
        .into(Alert)
        .values({
          alert_name: createAlertDto.alert_name,
          alert_description: createAlertDto.alert_description || '',
          parent_branch_id: user.parent_branch_id,
          alert_action_id: createAlertDto.alert_action_id,
          alert_event_id: createAlertDto.alert_event_id,
          alert_logical_condition_type:
            createAlertDto.alert_logical_condition_type,
          subject: createAlertDto.subject || '',
          message: createAlertDto.message || '',
          active: createAlertDto.active ?? true,
          created_by: user.id,
          updated_by: user.id,
        })
        .execute();

      const alertId = alertInsertResult.identifiers[0].id;

      // Create conditions using queryBuilder
      if (createAlertDto.conditions && createAlertDto.conditions.length > 0) {
        const conditionsToInsert = createAlertDto.conditions.map(
          conditionDto => {
            const conditionData: any = {
              alert_id: alertId,
              alert_condition_type_id: conditionDto.alert_condition_type_id,
              alert_operator_condition_type:
                conditionDto.alert_operator_condition_type,
              alert_condition_value_id: conditionDto.alert_condition_value_id,
              alert_condition_description:
                conditionDto.alert_condition_description || '',
              active: conditionDto.active ?? true,
              created_by: user.id,
              updated_by: user.id,
              created_at: new Date(),
              updated_at: new Date(),
            };

            // Set the appropriate field based on condition type
            switch (conditionDto.alert_condition_type_id) {
              case EConditionType.USER:
                conditionData.user_id = conditionDto.alert_condition_value_id;
                break;
              case EConditionType.ROLE:
                conditionData.role_id = conditionDto.alert_condition_value_id;
                break;
              case EConditionType.DAY_OF_MONTH:
                conditionData.day_of_month =
                  conditionDto.alert_condition_value_id;
                break;
              case EConditionType.DAY_OF_WEEK:
                conditionData.day_of_week =
                  conditionDto.alert_condition_value_id;
                break;
              case EConditionType.HOURS:
                conditionData.hours = conditionDto.alert_condition_value_id;
                break;
              case EConditionType.MINUTES:
                conditionData.minutes = conditionDto.alert_condition_value_id;
                break;
              case EConditionType.CHECKPOINT:
                conditionData.checkpoint_id =
                  conditionDto.alert_condition_value_id;
                break;
              case EConditionType.GEOFENCE:
                conditionData.geofence_id =
                  conditionDto.alert_condition_value_id;
                break;
              case EConditionType.ZONE:
                conditionData.zone_id = conditionDto.alert_condition_value_id;
                break;
              case EConditionType.DEVICE:
                conditionData.device_id = conditionDto.alert_condition_value_id;
                break;
            }

            return conditionData;
          },
        );

        await queryRunner.manager
          .createQueryBuilder()
          .insert()
          .into(AlertCondition)
          .values(conditionsToInsert)
          .execute();
      }

      // Create branches using queryBuilder
      if (createAlertDto.branch_ids && createAlertDto.branch_ids.length > 0) {
        const branchesToInsert = createAlertDto.branch_ids.map(branchId => ({
          alert_id: alertId,
          branch_id: branchId,
          active: true,
          created_by: user.id,
        }));

        await queryRunner.manager
          .createQueryBuilder()
          .insert()
          .into(AlertBranch)
          .values(branchesToInsert)
          .execute();
      }

      // Create recipients using queryBuilder
      if (createAlertDto.recipients && createAlertDto.recipients.length > 0) {
        const recipientsToInsert = createAlertDto.recipients.map(
          recipientDto => ({
            alert_id: alertId,
            recipient_type: recipientDto.recipient_type,
            recipient_contact: recipientDto.recipient_contact,
            created_by: user.id,
          }),
        );

        await queryRunner.manager
          .createQueryBuilder()
          .insert()
          .into(AlertRecipient)
          .values(recipientsToInsert)
          .execute();
      }

      await queryRunner.commitTransaction();
      return this.getAlertById(alertId, user, branch);
    } catch (err) {
      await queryRunner.rollbackTransaction();
      throw err;
    } finally {
      await queryRunner.release();
    }
  }

  async updateAlert(
    id: number,
    updateAlertDto: UpdateAlertDto,
    user: User,
    branch: Branch,
  ) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const alert = await this.alertRepository.findOne({ where: { id } });
      if (!alert) {
        throw new Error('Alert not found');
      }

      // Update alert using queryBuilder instead of active record pattern
      await queryRunner.manager
        .createQueryBuilder()
        .update(Alert)
        .set({
          alert_name: updateAlertDto.alert_name || '',
          alert_description: updateAlertDto.alert_description || '',
          alert_action_id: updateAlertDto.alert_action_id,
          alert_event_id: updateAlertDto.alert_event_id,
          alert_logical_condition_type:
            updateAlertDto.alert_logical_condition_type || null,
          subject: updateAlertDto.subject || '',
          message: updateAlertDto.message || '',
          active: updateAlertDto.active ?? true,
          updated_by: user.id,
          updated_at: new Date(),
        })
        .where('id = :id', { id })
        .execute();

      // Update conditions if provided
      if (updateAlertDto.conditions) {
        await this.alertConditionRepository.delete({ alert_id: id });

        const conditionsToInsert = updateAlertDto.conditions.map(
          conditionDto => {
            const conditionData: any = {
              alert_id: id,
              alert_condition_type_id: conditionDto.alert_condition_type_id,
              alert_operator_condition_type:
                conditionDto.alert_operator_condition_type,
              alert_condition_value_id: conditionDto.alert_condition_value_id,
              alert_condition_description:
                conditionDto.alert_condition_description || '',
              active:
                conditionDto.active === undefined
                  ? true
                  : (conditionDto.active ?? true),
              created_by: user.id,
              updated_by: user.id,
              created_at: new Date(),
              updated_at: new Date(),
            };

            // Set the appropriate field based on condition type
            switch (conditionDto.alert_condition_type_id) {
              case EConditionType.USER:
                conditionData.user_id = conditionDto.alert_condition_value_id;
                break;
              case EConditionType.ROLE:
                conditionData.role_id = conditionDto.alert_condition_value_id;
                break;
              case EConditionType.DAY_OF_MONTH:
                conditionData.day_of_month =
                  conditionDto.alert_condition_value_id;
                break;
              case EConditionType.DAY_OF_WEEK:
                conditionData.day_of_week =
                  conditionDto.alert_condition_value_id;
                break;
              case EConditionType.HOURS:
                conditionData.hours = conditionDto.alert_condition_value_id;
                break;
              case EConditionType.MINUTES:
                conditionData.minutes = conditionDto.alert_condition_value_id;
                break;
              case EConditionType.CHECKPOINT:
                conditionData.checkpoint_id =
                  conditionDto.alert_condition_value_id;
                break;
              case EConditionType.GEOFENCE:
                conditionData.geofence_id =
                  conditionDto.alert_condition_value_id;
                break;
              case EConditionType.ZONE:
                conditionData.zone_id = conditionDto.alert_condition_value_id;
                break;
              case EConditionType.DEVICE:
                conditionData.device_id = conditionDto.alert_condition_value_id;
                break;
            }

            return conditionData;
          },
        );

        if (conditionsToInsert.length > 0) {
          await queryRunner.manager
            .createQueryBuilder()
            .insert()
            .into(AlertCondition)
            .values(conditionsToInsert)
            .execute();
        }
      }

      // Update branches if provided
      if (updateAlertDto.branch_ids) {
        await this.alertBranchRepository.delete({ alert_id: id });

        if (updateAlertDto.branch_ids.length > 0) {
          const branchesToInsert = updateAlertDto.branch_ids.map(branchId => ({
            alert_id: id,
            branch_id: branchId,
            active: true,
            created_by: user.id,
          }));

          await queryRunner.manager
            .createQueryBuilder()
            .insert()
            .into(AlertBranch)
            .values(branchesToInsert)
            .execute();
        }
      }

      // Update recipients if provided
      if (updateAlertDto.recipients) {
        await this.alertRecipientRepository.delete({ alert_id: id });

        if (updateAlertDto.recipients.length > 0) {
          const recipientsToInsert = updateAlertDto.recipients.map(
            recipientDto => ({
              alert_id: id,
              recipient_type: recipientDto.recipient_type,
              recipient_contact: recipientDto.recipient_contact,
              created_by: user.id,
            }),
          );

          await queryRunner.manager
            .createQueryBuilder()
            .insert()
            .into(AlertRecipient)
            .values(recipientsToInsert)
            .execute();
        }
      }

      await queryRunner.commitTransaction();
      return this.getAlertById(id, user, branch);
    } catch (err) {
      await queryRunner.rollbackTransaction();
      throw err;
    } finally {
      await queryRunner.release();
    }
  }

  async deleteAlert(id: number, user: User, branch: Branch) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Verify alert exists and belongs to branch
      const alert = await this.getAlertById(id, user, branch);
      if (!alert) {
        throw new Error('Alert not found');
      }

      await queryRunner.manager.delete(AlertCondition, { alert_id: id });
      await queryRunner.manager.delete(AlertBranch, { alert_id: id });
      await queryRunner.manager.delete(AlertRecipient, { alert_id: id });
      await queryRunner.manager.delete(Alert, { id });

      await queryRunner.commitTransaction();
      return { message: 'Alert deleted successfully' };
    } catch (err) {
      await queryRunner.rollbackTransaction();
      throw err;
    } finally {
      await queryRunner.release();
    }
  }
}
