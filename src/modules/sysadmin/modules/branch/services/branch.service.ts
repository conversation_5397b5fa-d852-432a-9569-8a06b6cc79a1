import {
  ConflictException,
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Branch } from '../../../../../common/modules/database/entities/branch.entity';
import { Between, DataSource, Like, Not, Repository } from 'typeorm';
import { User } from '../../../../../common/modules/database/entities/user.entity';
import { Module } from '../../../../../common/modules/database/entities/module.entity';
import { Role } from '../../../../../common/modules/database/entities/role.entity';
import { RolePermission } from '../../../../../common/modules/database/entities/role-permission.entity';
import { UserBranch } from '../../../../../common/modules/database/entities/user-branch.entity';
import * as bcrypt from 'bcrypt';
import { CreateBranchDto } from '../dto/create-branch.dto';
import { UpdateBranchDto } from '../dto/update-branch.dto';
import { CodeGeneratorService } from '../../../../../common/services/code-generator.service';
import { isEmail } from 'class-validator';

@Injectable()
export class BranchService {
  constructor(
    private dataSource: DataSource,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Module)
    private moduleRepository: Repository<Module>,
    @InjectRepository(Branch)
    private branchRepository: Repository<Branch>,
    private codeGeneratorService: CodeGeneratorService, // Inject the service
  ) { }

  // Get Branches only main branch
  async getBranches(options: {
    search?: string;
    active?: boolean;
    pagination?: { limit: number; page: number };
    range_date?: { start: Date; end: Date };
    ordering: { field: string; direction: 'ASC' | 'DESC' };
  }) {
    const queryBuilder = this.branchRepository
      .createQueryBuilder('branch')
      .where('branch.parent_id = branch.id')
      .leftJoinAndSelect('branch.license', 'license')
      .leftJoinAndSelect('branch.timezone', 'timezone')
      .leftJoinAndSelect('branch.branch_owner_user', 'branch_owner_user');

    if (options.active !== undefined) {
      queryBuilder.andWhere('branch.active = :active', {
        active: options.active,
      });
    }

    if (options.search) {
      queryBuilder.andWhere('branch.branch_name LIKE :search', {
        search: `%${options.search}%`,
      });
    }

    if (options.range_date) {
      queryBuilder.andWhere('branch.created_at BETWEEN :start AND :end', {
        start: options.range_date.start,
        end: options.range_date.end,
      });
    }

    queryBuilder.orderBy(
      `branch.${options.ordering.field || 'created_at'}`,
      options.ordering.direction,
    );

    if (options.pagination) {
      queryBuilder
        .skip((options.pagination.page - 1) * options.pagination.limit)
        .take(options.pagination.limit);
    }

    const [items, total] = await queryBuilder.getManyAndCount();

    return {
      data: items,
      meta: {
        total,
        page: options.pagination?.page,
        limit: options.pagination?.limit,
        total_pages: options.pagination
          ? Math.ceil(total / options.pagination.limit)
          : 1,
      },
    };
  }

  // Creates branch with admin user and permissions
  async create(createBranchInput: CreateBranchDto, userLoggedIn: User) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Check email uniqueness
      const existingEmail = await this.userRepository.findOne({
        where: { email: createBranchInput.user.email },
      });

      if (existingEmail) {
        throw new ConflictException(
          `Email "${createBranchInput.user.email}" sudah digunakan`,
        );
      }

      // Check phone uniqueness
      const existingPhone = await this.userRepository.findOne({
        where: { phone: createBranchInput.user.phone },
      });

      if (existingPhone) {
        throw new ConflictException(
          `Nomor telepon "${createBranchInput.user.phone}" sudah digunakan`,
        );
      }

      // Use the code generator service
      const branchCode = await this.codeGeneratorService.generateBranchCode();

      // Create branch with code
      const branch = new Branch();
      branch.branch_code = branchCode;
      branch.branch_name = createBranchInput.name;
      branch.branch_description = createBranchInput.description;
      branch.created_by = userLoggedIn.id;
      branch.updated_by = userLoggedIn.id;
      branch.license_id = createBranchInput.license;
      branch.timezone_id = createBranchInput.timezone;
      branch.active = true; // Set default active ke true

      // Save branch first to get the ID
      const savedBranch = await queryRunner.manager.save(branch);

      // Set parent_id sama dengan id branch itu sendiri (self-referencing)
      savedBranch.parent_id = savedBranch.id;
      await queryRunner.manager.save(savedBranch);

      // Create Branch Admin role
      const roleBranchAdmin = new Role();
      roleBranchAdmin.role_name = 'Super Admin';
      roleBranchAdmin.parent_branch_id = savedBranch.id;
      roleBranchAdmin.active = true;
      roleBranchAdmin.created_by = userLoggedIn.id;
      roleBranchAdmin.updated_by = userLoggedIn.id;
      roleBranchAdmin.super_admin = true;
      await queryRunner.manager.save(roleBranchAdmin);

      // Create Admin role
      const roleAdmin = new Role();
      roleAdmin.role_name = 'Admin';
      roleAdmin.parent_branch_id = savedBranch.id;
      roleAdmin.active = true;
      roleAdmin.created_by = userLoggedIn.id;
      roleAdmin.updated_by = userLoggedIn.id;
      roleAdmin.super_admin = false;
      await queryRunner.manager.save(roleAdmin);

      // Setup permissions
      const modules = await this.moduleRepository.find();
      for (const module of modules) {
        const rolePermissionBranchAdmin = new RolePermission();
        rolePermissionBranchAdmin.role_id = roleBranchAdmin.id;
        rolePermissionBranchAdmin.module_id = module.id;
        rolePermissionBranchAdmin.allow_create = true;
        rolePermissionBranchAdmin.allow_update = true;
        rolePermissionBranchAdmin.allow_view = true;
        rolePermissionBranchAdmin.allow_delete = true;
        await queryRunner.manager.save(rolePermissionBranchAdmin);

        const rolePermissionAdmin = new RolePermission();
        rolePermissionAdmin.role_id = roleAdmin.id;
        rolePermissionAdmin.module_id = module.id;
        rolePermissionAdmin.allow_create = true;
        rolePermissionAdmin.allow_update = true;
        rolePermissionAdmin.allow_view = true;
        rolePermissionAdmin.allow_delete = true;
        await queryRunner.manager.save(rolePermissionAdmin);
      }

      // Create admin user
      const user = new User();
      user.parent_branch_id = savedBranch.id;
      user.role_id = roleBranchAdmin.id;
      user.name = createBranchInput.user.name;
      user.email = createBranchInput.user.email;
      user.password = await bcrypt.hash(createBranchInput.user.password, 10);
      user.phone = createBranchInput.user.phone;
      user.system_access = false;
      user.web_access = true;
      user.mobile_access = true;
      user.active = true;
      user.created_by = userLoggedIn.id;
      user.updated_by = userLoggedIn.id;
      await queryRunner.manager.save(user);

      // Set branch_owner_user_id
      savedBranch.branch_owner_user_id = user.id;
      savedBranch.branch_owner_user = user;
      await queryRunner.manager.save(savedBranch);

      // Update branch with former_user_id
      await queryRunner.manager.save(savedBranch);

      // Link user to branch
      const userBranch = new UserBranch();
      userBranch.user_id = user.id;
      userBranch.branch_id = savedBranch.id;
      userBranch.created_by = userLoggedIn.id;
      userBranch.updated_by = userLoggedIn.id;
      userBranch.active = true;
      await queryRunner.manager.save(userBranch);

      await queryRunner.commitTransaction();
      return {
        id: savedBranch.id,
        branch_name: savedBranch.branch_name,
        branch_description: savedBranch.branch_description,
        created_at: savedBranch.created_at,
        admin_user: {
          id: user.id,
          email: user.email,
          name: user.name,
        },
      };
    } catch (err) {
      console.error(err);
      await queryRunner.rollbackTransaction();
      if (err instanceof ConflictException) {
        throw err;
      }
      throw new Error('Failed to create branch');
    } finally {
      await queryRunner.release();
    }
  }

  // Get single branch by ID
  async getBranchById(id: number) {
    return await this.branchRepository.findOne({
      where: { id },
      relations: ['license', 'timezone', 'branch_owner_user'],
    });
  }

  // Update branch by ID
  async update(
    id: number,
    updateBranchDto: UpdateBranchDto,
    userLoggedIn: User,
  ) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Check if branch exists
      const branch = await queryRunner.manager.findOne(Branch, {
        where: { id },
        relations: ['branch_owner_user'],
      });

      if (!branch) {
        throw new NotFoundException(`Branch with ID ${id} not found`);
      }

      // Update branch data
      branch.branch_name = updateBranchDto.name;
      branch.branch_description = updateBranchDto.description;
      branch.license_id = updateBranchDto.license;
      branch.timezone_id = updateBranchDto.timezone;
      branch.active = updateBranchDto.active;
      branch.updated_by = userLoggedIn.id;
      branch.updated_at = new Date();

      // Save updated branch
      const updatedBranch = await queryRunner.manager.save(branch);

      // Update admin user data
      const adminUser = await queryRunner.manager.findOne(User, {
        where: { id: branch.branch_owner_user.id },
      });

      if (!adminUser) {
        throw new NotFoundException(
          `Admin user for branch with ID ${id} not found`,
        );
      }

      if (
        !(await this.isEmailAvailable(
          updateBranchDto.admin_email,
          adminUser.email,
        ))
      ) {
        throw new BadRequestException('Email is already in use');
      }

      if (
        !(await this.isPhoneAvailable(
          updateBranchDto.admin_phone,
          adminUser.phone,
        ))
      ) {
        throw new BadRequestException('Phone number is already in use');
      }

      adminUser.name = updateBranchDto.admin_name;
      adminUser.email = updateBranchDto.admin_email;
      adminUser.phone = updateBranchDto.admin_phone;
      adminUser.updated_by = userLoggedIn.id;
      adminUser.updated_at = new Date();

      // Update admin user password if provided
      if (updateBranchDto.admin_password) {
        adminUser.password = await bcrypt.hash(
          updateBranchDto.admin_password,
          10,
        );
      }

      await queryRunner.manager.save(adminUser);

      // update branches.license_id
      await queryRunner.manager.update(Branch, {
        parent_id: branch.id,
      }, {
        license_id: updateBranchDto.license
      });

      await queryRunner.commitTransaction();
      return {
        id: updatedBranch.id,
        branch_name: updatedBranch.branch_name,
        branch_description: updatedBranch.branch_description,
        active: updatedBranch.active,
        updated_at: updatedBranch.updated_at,
      };
    } catch (err) {
      await queryRunner.rollbackTransaction();
      throw err;
    } finally {
      await queryRunner.release();
    }
  }

  // Check if email is valid and not exists except current email
  private async isEmailAvailable(
    email: string,
    currentEmail: string,
  ): Promise<boolean> {
    console.log(email, currentEmail);
    // If the email hasn't changed, it's valid
    if (email === currentEmail) {
      return true;
    }

    const existingEmail = await this.userRepository.findOne({
      where: { email },
    });

    if (existingEmail) {
      return false;
    }

    return true;
  }

  // Check if phone is valid except current phone
  private async isPhoneAvailable(
    phone: string,
    currentPhone: string,
  ): Promise<boolean> {
    // If the phone hasn't changed, it's valid
    if (phone === currentPhone) {
      return true;
    }

    const existingPhone = await this.userRepository.findOne({
      where: { phone },
    });

    if (existingPhone) {
      return false;
    }

    return true;
  }
}
