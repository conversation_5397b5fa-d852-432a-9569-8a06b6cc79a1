import { Module } from '@nestjs/common';
import { BatteryLevelTaskScheduledModule } from './modules/battery-level/battery-level.task-scheduled.module';
import { ActivityLogTaskScheduledModule } from './modules/activity-log/activity-log.task-scheduled.module';
import { AlarmTaskScheduledModule } from './modules/alarm/alarm.task-scheduled.module';
import { AverageRotationTaskScheduledModule } from './modules/average-rotation/average-rotation.task-scheduled.module';
import { BranchDetailTaskScheduledModule } from './modules/branch-detail/branch-detail.task-scheduled.module';
import { CheckpointActivityTaskScheduledModule } from './modules/checkpoint-activity/checkpoint-activity.task-scheduled.module';
import { ExceptionTaskScheduledModule } from './modules/exception/exception.task-scheduled.module';
import { ExceptionDetailedTaskScheduledModule } from './modules/exception-detailed/exception-detailed.task-scheduled.module';
import { SignInOutTaskScheduledModule } from './modules/sign-in-out/sign-in-out.task-scheduled.module';
import { TaskTaskScheduledModule } from './modules/task/task.task-scheduled.module';
import { TimeRotationTaskScheduledModule } from './modules/time-rotation/time-rotation.task-scheduled.module';
import { TimeOnZoneTaskScheduledModule } from './modules/time-on-zone/time-on-zone.task-scheduled.module';
import { FormTaskScheduledModule } from './modules/form/form.task-scheduled.module';
import { GeofenceTaskScheduledModule } from './modules/geofence/geofence.task-scheduled.module';
import { MissedZoneTaskScheduledModule } from './modules/missed-zone/missed-zone.task-scheduled.module';
import { SendgridModule } from '../../common/modules/mailer/sendgrid/sendgrid.module';
import { TaskScheduledService } from './services/task-scheduled.service';
import { TaskScheduledController } from './controllers/task-scheduled.controller';
import { ForceSignoutService } from './services/force-signout.service';

@Module({
  imports: [
    // ScheduleModule.forRoot(),
    ActivityLogTaskScheduledModule,
    AlarmTaskScheduledModule,
    AverageRotationTaskScheduledModule,
    BranchDetailTaskScheduledModule,
    CheckpointActivityTaskScheduledModule,
    BatteryLevelTaskScheduledModule,
    ExceptionTaskScheduledModule,
    ExceptionDetailedTaskScheduledModule,
    FormTaskScheduledModule,
    GeofenceTaskScheduledModule,
    MissedZoneTaskScheduledModule,
    SignInOutTaskScheduledModule,
    TaskTaskScheduledModule,
    TimeRotationTaskScheduledModule,
    TimeOnZoneTaskScheduledModule,
    SendgridModule,
  ],
  providers: [TaskScheduledService, ForceSignoutService],
  controllers: [TaskScheduledController],
})
export class TaskScheduledModule {}
