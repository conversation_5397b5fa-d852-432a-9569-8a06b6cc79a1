import { Injectable, Logger } from '@nestjs/common';
import { Scheduler } from '../../../common/modules/database/entities/scheduler.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { ActivityLogTaskScheduledService } from '../modules/activity-log/services/activity-log.task-scheduled.service';
import { AlarmTaskScheduledService } from '../modules/alarm/services/alarm.task-scheduled.service';
import { AverageRotationTaskScheduledService } from '../modules/average-rotation/services/average-rotation.task-scheduled.service';
import { BranchDetailTaskScheduledService } from '../modules/branch-detail/services/branch-detail.task-scheduled.service';
import { CheckpointActivityTaskScheduledService } from '../modules/checkpoint-activity/services/checkpoint-activity.task-scheduled.service';
import { BatteryLevelTaskScheduledService } from '../modules/battery-level/services/battery-level.task-scheduled.service';
import { ExceptionTaskScheduledService } from '../modules/exception/services/exception.task-scheduled.service';
import { ExceptionDetailedTaskScheduledService } from '../modules/exception-detailed/services/exception-detailed.task-scheduled.service';
import { FormTaskScheduledService } from '../modules/form/services/form.task-scheduled.service';
import { GeofenceTaskScheduledService } from '../modules/geofence/services/geofence.task-scheduled.service';
import { MissedZoneTaskScheduledService } from '../modules/missed-zone/services/missed-zone.task-scheduled.service';
import { SignInOutTaskScheduledService } from '../modules/sign-in-out/services/sign-in-out.task-scheduled.service';
import { TaskTaskScheduledService } from '../modules/task/services/task.task-scheduled.service';
import { TimeOnZoneTaskScheduledService } from '../modules/time-on-zone/services/time-on-zone.task-scheduled.service';
import { TimeRotationTaskScheduledService } from '../modules/time-rotation/services/time-rotation.task-scheduled.service';
import { ISchedulerService } from '../interfaces/task-scheduled.interfaces';
import { SendgridService } from '../../../common/modules/mailer/sendgrid/services/sendgrid.service';

import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';
import { SendgridSendMessageInterface } from '../../../common/modules/mailer/sendgrid/interfaces/sendgird.interface';
import { updateSchedulerNextRunTime } from '../../../common/utils/scheduler.util';

dayjs.extend(utc);

// Report type name mapping
const REPORT_TYPE_NAMES = {
  1: 'Activity Log',
  2: 'Alarm',
  3: 'Average Rotation',
  4: 'Branch Detail',
  5: 'Checkpoint Activity',
  6: 'Battery Level',
  7: 'Exception',
  8: 'Exception Detailed',
  9: 'Form',
  10: 'Geofence',
  12: 'Missed Zone',
  13: 'Sign In Out',
  14: 'Task',
  15: 'Time Rotation',
  16: 'Time On Zone',
};

@Injectable()
export class TaskScheduledService {
  private readonly logger = new Logger(TaskScheduledService.name);
  private serviceMap: Record<number, ISchedulerService<any>>;

  constructor(
    @InjectRepository(Scheduler)
    private schedulerRepository: Repository<Scheduler>,
    private sendgridService: SendgridService,
    private activityLogTaskScheduledService: ActivityLogTaskScheduledService,
    private alarmTaskScheduledService: AlarmTaskScheduledService,
    private averageRotationTaskScheduledService: AverageRotationTaskScheduledService,
    private branchDetailTaskScheduledService: BranchDetailTaskScheduledService,
    private checkpointActivityTaskScheduledService: CheckpointActivityTaskScheduledService,
    private batteryLevelTaskScheduledService: BatteryLevelTaskScheduledService,
    private exceptionTaskScheduledService: ExceptionTaskScheduledService,
    private exceptionDetailedTaskScheduledService: ExceptionDetailedTaskScheduledService,
    private formTaskScheduledService: FormTaskScheduledService,
    private geofenceTaskScheduledService: GeofenceTaskScheduledService,
    private missedZoneTaskScheduledService: MissedZoneTaskScheduledService,
    private signInOutTaskScheduledService: SignInOutTaskScheduledService,
    private taskTaskScheduledService: TaskTaskScheduledService,
    private timeRotationTaskScheduledService: TimeRotationTaskScheduledService,
    private timeOnZoneTaskScheduledService: TimeOnZoneTaskScheduledService,
  ) {
    this.initServiceMap();
  }

  async run() {
    const startTime = Date.now();
    const currentDate = dayjs().utc().second(0).millisecond(0);
    const schedulers = await this.getScheduler(currentDate);

    this.logger.log(`Processing ${schedulers.length} scheduled reports`);

    const processingPromises = schedulers.map(scheduler => {
      const reportTypeId = Number(scheduler.report_type_id);
      return this.processScheduledReport(scheduler, reportTypeId);
    });

    await Promise.all(processingPromises);

    const executionTime = Date.now() - startTime;
    this.logger.log(`Completed processing in ${executionTime}ms`);
  }

  async getScheduler(date: dayjs.Dayjs) {
    const currentDate = date.second(0).millisecond(0);
    const formattedDateTime = currentDate.format('YYYY-MM-DD HH:mm:ss') + '+00';

    return await this.schedulerRepository
      .createQueryBuilder('scheduler')
      .where('scheduler.active = true')
      .andWhere('scheduler.next_run_time = :currentDateTime::timestamptz', {
        currentDateTime: formattedDateTime,
      })
      .leftJoinAndSelect('scheduler.branch', 'branch')
      .leftJoinAndSelect('branch.timezone', 'timezone')
      .leftJoinAndSelect('scheduler.selected_branch', 'selected_branch')
      .leftJoinAndSelect('scheduler.role', 'role')
      .leftJoinAndSelect('scheduler.user', 'user')
      .leftJoinAndSelect('scheduler.report_type', 'report_type')
      .leftJoinAndSelect('scheduler.device', 'device')
      .leftJoinAndSelect('scheduler.zone', 'zone')
      .leftJoinAndSelect('scheduler.checkpoint', 'checkpoint')
      .leftJoinAndSelect('scheduler.form', 'form')
      .leftJoinAndSelect('scheduler.activity', 'activity')
      .leftJoinAndSelect('scheduler.task', 'task')
      .leftJoinAndSelect('scheduler.frequency', 'frequency')
      .leftJoinAndSelect('scheduler.recipients', 'recipients')
      .leftJoinAndSelect('scheduler.checkpoint_labels', 'checkpoint_labels')
      .leftJoinAndSelect('scheduler.device_labels', 'device_labels')
      .leftJoinAndSelect('scheduler.user_labels', 'user_labels')
      .leftJoinAndSelect('scheduler.zone_labels', 'zone_labels')
      .getMany();
  }

  private initServiceMap() {
    this.serviceMap = {
      1: this.activityLogTaskScheduledService,
      2: this.alarmTaskScheduledService,
      3: this.averageRotationTaskScheduledService,
      4: this.branchDetailTaskScheduledService,
      5: this.checkpointActivityTaskScheduledService,
      6: this.batteryLevelTaskScheduledService,
      7: this.exceptionTaskScheduledService,
      8: this.exceptionDetailedTaskScheduledService,
      9: this.formTaskScheduledService,
      10: this.geofenceTaskScheduledService,
      12: this.missedZoneTaskScheduledService,
      13: this.signInOutTaskScheduledService,
      14: this.taskTaskScheduledService,
      15: this.timeRotationTaskScheduledService,
      16: this.timeOnZoneTaskScheduledService,
    };
  }

  private async processScheduledReport(
    scheduler: Scheduler,
    reportTypeId: number,
  ) {
    await updateSchedulerNextRunTime(this.schedulerRepository, scheduler);
    const reportTypeName =
      REPORT_TYPE_NAMES[reportTypeId] || `Report Type ${reportTypeId}`;

    try {
      this.logger.debug(
        `Processing ${reportTypeName} report for scheduler ID: ${scheduler.id}`,
      );

      const service = this.serviceMap[reportTypeId];
      if (!service) {
        throw new Error(`No service found for report type ID: ${reportTypeId}`);
      }

      const results = await service.fetchDataAndGenerateDocument(scheduler);

      if (results.total === 0 && scheduler.stop_if_blank) {
        this.logger.debug(
          `Skipping empty report for scheduler ID: ${scheduler.id}`,
        );
        return;
      }

      const payloadSendEmails: SendgridSendMessageInterface = {
        to: scheduler.recipients.map(recipient => recipient.recipient_contact),
        subject: scheduler.subject,
        html: scheduler.message,
        attachments: results.attachments || [],
        text: scheduler.message,
      };

      await this.sendgridService.sendEmail(payloadSendEmails);
      this.logger.debug(
        `Email sent for scheduler ID: ${scheduler.id}, recipients: ${payloadSendEmails.to.length}`,
      );
    } catch (error) {
      this.logger.error(
        `Error processing ${reportTypeName} report for scheduler ID: ${scheduler.id}`,
        error.stack,
      );
    }
  }
}
