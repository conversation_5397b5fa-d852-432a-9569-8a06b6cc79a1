import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import * as dayjs from "dayjs";
import { AuthAccessToken } from '../../../common/modules/database/entities/auth-access-token.entity';

@Injectable()
export class ForceSignoutService {
    constructor(
        @InjectRepository(AuthAccessToken) private readonly authAccessTokenRepository: Repository<AuthAccessToken>
    ) {}

    async forceSignout(): Promise<void> {
        const now = dayjs();

        // Fetch all tokens
        const tokens = await this.authAccessTokenRepository.find();

        // Filter tokens that expire in more than 24 hours
        const tokensToSignOut = tokens.filter(token => {
            const expiresAt = dayjs(token.expires_at);
            return now.isAfter(expiresAt);
        });

        // Delete tokens that meet the criteria
        if (tokensToSignOut.length > 0) {
            const tokenIds = tokensToSignOut.map(token => token.id);
            await this.authAccessTokenRepository.delete(tokenIds);
        }
    }
}
