import { Controller, Post, Body, Get, Query } from '@nestjs/common';
import { TaskScheduledService } from '../services/task-scheduled.service';
import { ForceSignoutService } from '../services/force-signout.service';
import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

@Controller('task-scheduled')
export class TaskScheduledController {
  constructor(
    private readonly taskScheduledService: TaskScheduledService,
    private readonly forceSignoutService: ForceSignoutService,
  ) {}

  /**
   * Main endpoint for Google Cloud Scheduler to trigger the task
   * This replaces the @Cron('0 * * * *') decorator in the service
   */
  @Post('run')
  async runScheduledTasks() {
    const result = await this.taskScheduledService.run();
    return { success: true, message: 'Scheduled tasks executed successfully' };
  }

  @Post('force-signout')
  async forceSignout() {
    await this.forceSignoutService.forceSignout();
  }

  /**
   * Get all scheduled reports that would run at a specific time
   * Useful for testing or checking scheduled reports
   */
  @Get('reports')
  async getScheduledReports(@Query('timestamp') timestamp?: string) {
    const date = timestamp
      ? dayjs(timestamp).utc().second(0).millisecond(0)
      : dayjs().utc().second(0).millisecond(0);

    const scheduledReports = await this.taskScheduledService.getScheduler(date);
    return {
      timestamp: date.format('YYYY-MM-DD HH:mm:ss'),
      count: scheduledReports.length,
      reports: scheduledReports,
    };
  }

}
