import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ApplyFormLogDto, FormLogFieldDto } from '../dto/apply-form.dto';
import { InjectRepository } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import { Repository, DataSource } from 'typeorm';
import { <PERSON><PERSON><PERSON>eader } from '../../../../../common/decorators/current-device.decorator';
import { Device } from '../../../../../common/modules/database/entities/device.entity';
import { Form } from '../../../../../common/modules/database/entities/form.entity';
import { LogAlert } from '../../../../../common/modules/database/entities/log-alert.entity';
import { LogFormField } from '../../../../../common/modules/database/entities/log-form-field.entity';
import { LogForm } from '../../../../../common/modules/database/entities/log-form.entity';
import { User } from '../../../../../common/modules/database/entities/user.entity';
import { StorageService } from '../../../../../common/modules/storage/services/storage.service';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class FormLogService {
  constructor(
    @InjectRepository(Form)
    private readonly formRepository: Repository<Form>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Device)
    private deviceRepository: Repository<Device>,
    @Inject(DataSource)
    private dataSource: DataSource,
    private storageService: StorageService,
  ) {}

  async apply(
    idForm: string,
    data: ApplyFormLogDto,
    user: User,
    files: Express.Multer.File[],
    deviceFromHeader: DeviceHeader,
  ) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    const uploadedFiles: string[] = [];

    try {
      let device: Device | null = null;
      // Validate Device
      if (deviceFromHeader.device_uid) {
        const getDevice = await this.deviceRepository.findOne({
          where: {
            imei: deviceFromHeader.device_uid,
          },
        });
        if (!getDevice) {
          throw new NotFoundException('Device not found');
        }
        device = getDevice;
      }

      // Validate files first
      this.validateFiles(data, files);

      // Fetch form with validation and relations
      const form = await this.formRepository.findOne({
        where: {
          id: parseInt(idForm),
          parent_branch_id: user.parent_branch_id,
        },
        relations: ['fields', 'parent_branch', 'fields.field_type'],
      });

      if (!form) {
        throw new NotFoundException('Form not found');
      }

      // Validate form fields
      this.validateFormFields(form, data.fields);

      // Create log form
      const logForm = this.createLogForm(form, user, data, device);
      await queryRunner.manager.save(LogForm, logForm);

      // Process fields and files
      const logFormFields = await this.processFieldsAndFiles(
        data.fields,
        files,
        logForm.id,
        uploadedFiles,
      );
      await queryRunner.manager.save(LogFormField, logFormFields);

      // Create alert log
      const logAlert = this.createAlertLog(form, logForm, logFormFields, user);
      await queryRunner.manager.save(LogAlert, logAlert);

      await queryRunner.commitTransaction();
      return logForm;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      await this.deleteUploadedFiles(uploadedFiles);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  private createLogForm(
    form: Form,
    user: User,
    data: ApplyFormLogDto,
    device: Device | null,
  ): LogForm {
    const logForm = new LogForm();
    logForm.uuid = uuidv4();
    logForm.form_id = form.id;
    logForm.form_name = form.form_name;
    logForm.parent_branch_id = form.parent_branch_id;
    logForm.role_id = user.role.id;
    logForm.role_name = user.role.role_name;
    logForm.user_id = user.id;
    logForm.user_name = user.name;
    logForm.latitude = data.latitude;
    logForm.longitude = data.longitude;
    if (device) {
      logForm.device_id = device.id;
      logForm.device_name = device.device_name;
    }
    logForm.timezone_id = user.parent_branch.timezone.id;
    logForm.timezone_name = user.parent_branch.timezone.timezone_name;
    logForm.original_submitted_time = dayjs(
      data.original_submitted_time,
    ).toDate();
    logForm.event_time = dayjs().toDate();

    return logForm;
  }

  private async processFieldsAndFiles(
    fields: FormLogFieldDto[],
    files: Express.Multer.File[],
    logFormId: number,
    uploadedFiles: string[],
  ): Promise<LogFormField[]> {
    const logFormFields: LogFormField[] = [];

    for (const field of fields) {
      if (['image', 'signature'].includes(field.field_type_name)) {
        const file = files.find(f => f.fieldname === `file_${field.id}`);
        if (!file) {
          throw new NotFoundException(`File not found for field ${field.id}`);
        }

        const uuidFilename = uuidv4();
        const extension = file.originalname.split('.').pop();
        const fileName = `log-form/${uuidFilename}.${extension}`;

        const photoPublicUrl = await this.storageService.uploadFile(
          file,
          fileName,
        );
        field.value = photoPublicUrl;
        uploadedFiles.push(fileName);
      }

      logFormFields.push(this.createLogFormField(field, logFormId));
    }

    return logFormFields;
  }

  private createLogFormField(
    field: FormLogFieldDto,
    logFormId: number,
  ): LogFormField {
    const logFormField = new LogFormField();
    logFormField.log_form_id = logFormId;
    logFormField.field_type_id = parseInt(field.field_type_id);
    logFormField.field_type_name = field.field_type_name;
    logFormField.form_field_name = field.form_field_name;
    logFormField.field_type_value = field.value;

    return logFormField;
  }

  private createAlertLog(
    form: Form,
    logForm: LogForm,
    data: LogFormField[],
    user: User,
  ): LogAlert {
    const logAlert = new LogAlert();
    logAlert.uuid = uuidv4();
    logAlert.alert_event_id = 3;
    logAlert.alert_event_name = 'Form Submitted';
    logAlert.log_id = logForm.id;
    logAlert.log_uuid = logForm.uuid;
    logAlert.reference_name = form.form_name;
    logAlert.parent_branch_id = user.parent_branch.parent_id;
    logAlert.branch_id = user.parent_branch.id;
    logAlert.user_id = user.id;
    logAlert.role_id = user.role.id;
    logAlert.payload_data = {
      type: 'form',
      form: form,
      logForm: logForm,
      fields: data,
    };
    logAlert.deleted_on_dashboard = false;
    logAlert.original_submitted_time = logForm.original_submitted_time;
    logAlert.event_time = logForm.event_time;

    return logAlert;
  }

  private validateFiles(data: ApplyFormLogDto, files: Express.Multer.File[]) {
    // Create a map of expected photo fields
    const expectedPhotos = new Map<string, boolean>();
    const receivedPhotos = new Map<string, boolean>();

    // Collect expected photo fields
    data.fields.forEach(field => {
      if (
        field.field_type_name === 'image' ||
        field.field_type_name === 'signature'
      ) {
        expectedPhotos.set(field.value, true);
      }
    });

    // Check received files
    files.forEach(file => {
      // Validate file name format
      if (!file.fieldname.match(/^file_\d+$/)) {
        throw new BadRequestException(
          `Invalid file field name: ${file.fieldname}. Expected format: file_[ID]`,
        );
      }

      // Mark as received
      receivedPhotos.set(file.fieldname, true);

      // Check if this photo was expected
      if (!expectedPhotos.has(file.fieldname)) {
        throw new BadRequestException(
          `Unexpected file received: ${file.fieldname}`,
        );
      }

      // Validate file type
      if (!file.mimetype.startsWith('image/')) {
        throw new BadRequestException(
          `Invalid file type for ${file.fieldname}. Only images are allowed`,
        );
      }
    });

    // Check if all expected photos were received
    expectedPhotos.forEach((_, photoField) => {
      if (!receivedPhotos.has(photoField)) {
        throw new BadRequestException(
          `Missing required photo file: ${photoField}`,
        );
      }
    });
  }

  private validateFormFields(form: Form, submittedFields: any[]) {
    // Create map of registered form fields
    const formFields = new Map(
      form.fields.map(field => [field.id.toString(), field]),
    );

    // Validate each submitted field
    for (const field of submittedFields) {
      const formField = formFields.get(field.id);

      if (!formField) {
        throw new BadRequestException(
          `Field with ID ${field.id} is not registered in this form`,
        );
      }

      // Validate field type
      if (formField.field_type_id.toString() !== field.field_type_id) {
        throw new BadRequestException(
          `Invalid field type for field ${field.id}. Expected: ${formField.field_type_id}, got: ${field.field_type_id}`,
        );
      }

      // Validate field name
      if (formField.form_field_name !== field.form_field_name) {
        throw new BadRequestException(
          `Invalid field name for field ${field.id}. Expected: ${formField.form_field_name}, got: ${field.form_field_name}`,
        );
      }
    }
  }

  private async deleteUploadedFiles(uploadedFiles: string[]) {
    try {
      await this.storageService.deleteFiles(uploadedFiles);
    } catch (error) {
      console.error('Failed to delete uploaded files:', error);
    }
  }
}
