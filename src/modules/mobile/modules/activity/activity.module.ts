import { Modu<PERSON> } from '@nestjs/common';
import { ActivityController } from './controllers/activity.controller';
import { ActivityService } from './service/activity.service';
import { ActivityLogController } from './controllers/activity-log.controller';
import { ActivityLogService } from './service/activity-log.service';
import { SendgridModule } from '../../../../common/modules/mailer/sendgrid/sendgrid.module';
import { StorageModule } from '../../../../common/modules/storage/storage.module';
import { AlertCommonModule } from '../../../../common/modules/alert/alert-common.module';

@Module({
  imports: [SendgridModule, StorageModule, AlertCommonModule],
  controllers: [ActivityController, ActivityLogController],
  providers: [ActivityService, ActivityLogService],
  exports: [],
})
export class ActivityModule {}
