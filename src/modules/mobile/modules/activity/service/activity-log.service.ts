import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import { DataSource, Repository } from 'typeorm';
import { <PERSON><PERSON><PERSON>eader } from '../../../../../common/decorators/current-device.decorator';
import { Activity } from '../../../../../common/modules/database/entities/activity.entity';
import { Device } from '../../../../../common/modules/database/entities/device.entity';
import { LogActivity } from '../../../../../common/modules/database/entities/log-activity.entity';
import { Role } from '../../../../../common/modules/database/entities/role.entity';
import { User } from '../../../../../common/modules/database/entities/user.entity';
import { StorageService } from '../../../../../common/modules/storage/services/storage.service';
import { ApplyActivityDto } from '../dto/apply-activity.dto';
import { v4 as uuidv4 } from 'uuid';
import { AlertCommonService } from '../../../../../common/modules/alert/services/alert-common.service';

@Injectable()
export class ActivityLogService {
  constructor(
    @InjectRepository(Activity)
    private activityRepository: Repository<Activity>,
    @InjectRepository(Role)
    private role: Repository<Role>,
    @InjectRepository(Device)
    private deviceRepository: Repository<Device>,
    private alertCommonService: AlertCommonService,
    private storageService: StorageService,
    private dataSource: DataSource,
  ) {}

  async apply(
    body: ApplyActivityDto,
    user: User,
    idActivity: number,
    file: Express.Multer.File,
    deviceFromHeader: DeviceHeader,
  ) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      let device: Device | null = null;
      // Validate Device
      if (deviceFromHeader.device_uid) {
        const getDevice = await this.deviceRepository.findOne({
          where: {
            imei: deviceFromHeader.device_uid,
          },
        });
        if (!getDevice) {
          throw new NotFoundException('Device not found');
        }
        device = getDevice;
      }

      // Validate activity exists
      const activity = await this.activityRepository.findOne({
        where: { id: idActivity },
        relations: [
          'branch',
          'parent_branch',
          'parent_branch',
          'branch.timezone',
        ],
      });
      if (!activity) {
        throw new NotFoundException('Activity not found');
      }

      // Validate role
      const role = await this.role.findOne({
        where: { id: user.role_id, parent_branch_id: user.parent_branch_id },
      });
      if (!role) {
        throw new NotFoundException('Role not found');
      }

      // Validate requirements
      this.validateActivityRequirements(activity, body, file);

      // Upload photo if exists
      const photoUrl = await this.uploadPhoto(file);

      // Create and save log activity
      const logActivity = this.createLogActivity(
        activity,
        body,
        user,
        role,
        photoUrl,
        device,
      );
      const savedLogActivity = await queryRunner.manager.save(
        LogActivity,
        logActivity,
      );

      await queryRunner.commitTransaction();

      this.sendAlert(savedLogActivity).then().catch();

      return {
        message: 'Log activity created successfully',
        data: savedLogActivity,
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  public async sendAlert(logActivity: LogActivity) {
    return this.alertCommonService.processAlert({
      alertEventId: 1,
      logActivity: logActivity,
      userId: logActivity.user_id,
      roleId: logActivity.role_id,
      submittedDateTime: dayjs(
        logActivity.original_submitted_time,
      ).toISOString(),
      deviceId: logActivity.device_id,
      parentBranchId: logActivity.parent_branch_id,
    });
  }

  private validateActivityRequirements(
    activity: Activity,
    body: ApplyActivityDto,
    file?: Express.Multer.File,
  ) {
    if (activity.gps_required && (!body.latitude || !body.longitude)) {
      throw new BadRequestException('Latitude and longitude are required');
    }
    if (activity.photo_required && !file) {
      throw new BadRequestException('Photo is required');
    }
    if (activity.comment_required && !body.comment) {
      throw new BadRequestException('Comment is required');
    }
  }

  private async uploadPhoto(file?: Express.Multer.File): Promise<string> {
    if (!file) return '';

    const fileUuid = uuidv4();
    const fileExtension = file.originalname.split('.').pop();
    return await this.storageService.uploadFile(
      file,
      `log-activity/${fileUuid}.${fileExtension}`,
    );
  }

  private createLogActivity(
    activity: Activity,
    body: ApplyActivityDto,
    user: User,
    role: Role,
    photoUrl: string,
    device: Device | null,
  ): LogActivity {
    const logActivity = new LogActivity();

    logActivity.uuid = uuidv4();
    logActivity.parent_branch_id = user.parent_branch_id;
    logActivity.branch_id = activity.branch_id;
    logActivity.branch_name = activity.branch.branch_name;
    logActivity.activity_id = activity.id;
    logActivity.activity_name = activity.activity_name;
    logActivity.role_id = user.role_id;
    logActivity.role_name = role.role_name;
    logActivity.timezone_id = activity.branch.timezone.id;
    logActivity.timezone_name = activity.branch.timezone.timezone_name;
    logActivity.user_id = user.id;
    logActivity.user_name = user.name;

    if (body.latitude && body.longitude) {
      logActivity.latitude = body.latitude;
      logActivity.longitude = body.longitude;
    }

    if (photoUrl) {
      logActivity.photo_url = photoUrl;
      logActivity.photo_thumbnail_url = photoUrl;
    }

    if (body.comment) {
      logActivity.comment = body.comment;
    }

    if (device) {
      logActivity.device_id = device.id;
      logActivity.device_name = device.device_name;
    }

    logActivity.original_submitted_time = dayjs(
      body.original_submitted_time,
    ).toDate();
    logActivity.event_time = new Date();

    return logActivity;
  }
}
