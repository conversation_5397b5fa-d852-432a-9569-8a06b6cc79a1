import { TaskLogService } from './task-log.service';
import { Repository } from 'typeorm';
import { LogTask } from '../../../../../common/modules/database/entities/log-task.entity';
import { Test } from '@nestjs/testing';
import { AppModule } from '../../../../../app.module';
import { getRepositoryToken } from '@nestjs/typeorm';

describe('TaskLogService', () => {
  let taskLogService: TaskLogService;
  let taskLogRepository: Repository<LogTask>;
  let logTask: LogTask;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    taskLogService = module.get<TaskLogService>(TaskLogService);
    taskLogRepository = module.get<Repository<LogTask>>(
      getRepositoryToken(LogTask),
    );
  });

  it('should be defined', () => {
    expect(taskLogService).toBeDefined();
    expect(taskLogRepository).toBeDefined();
  });

  describe('Send Alert', () => {
    beforeEach(async () => {
      const getLogTask = await taskLogRepository.findOne({
        where: { id: 21 },
      });

      if (!getLogTask) {
        throw new Error('Log task not found');
      }

      logTask = getLogTask;
    });

    it('should defined log task', async () => {
      expect(logTask).toBeDefined();
    });

    it('should send alert', async () => {
      const result = await taskLogService.sendAlert(logTask);

      // Greater than 0 because it's an array
      expect(result).toBeDefined();
      expect(result.length).toBeGreaterThan(0);
    });
  });
});
