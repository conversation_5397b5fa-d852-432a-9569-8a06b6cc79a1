import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { TaskController } from './controllers/task.controller';
import { TaskService } from './services/task.service';
import { TaskLogController } from './controllers/task-log.controller';
import { TaskLogService } from './services/task-log.service';
import { AlertCommonModule } from '../../../../common/modules/alert/alert-common.module';
import { StorageModule } from '../../../../common/modules/storage/storage.module';

@Module({
  imports: [
    StorageModule,
    AlertCommonModule,
  ],
  controllers: [
    TaskController,
    TaskLogController,
  ],
  providers: [TaskService, TaskLogService],
  exports: [],
})
export class TaskModule {}
