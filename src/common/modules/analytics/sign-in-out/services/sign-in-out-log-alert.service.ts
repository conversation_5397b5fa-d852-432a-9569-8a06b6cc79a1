import { Injectable } from '@nestjs/common';
import { LogUserDevice, UserLogType } from '../../../database/entities/log-user-device.entity';
import { User } from '../../../database/entities/user.entity';
import { LogAlert } from '../../../database/entities/log-alert.entity';
import { v4 as uuidv4 } from 'uuid';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class SignInOutLogAlertService {
  constructor(
    @InjectRepository(LogAlert)
    private readonly logAlertRepository: Repository<LogAlert>,
  ) {}

  public async createLogAlert(
    logUserDevice: LogUserDevice,
    user: User,
  ) {
    // Check if logUserDevice already has a logAlert
    const existingLogAlert = await this.logAlertRepository.findOne({
      where: { log_id: logUserDevice.id },
    });

    if (existingLogAlert) {
      return existingLogAlert;
    }

    const logAlert = new LogAlert();

    logAlert.uuid = uuidv4();

    // Set alert event ID and name based on user log type
    if (logUserDevice.user_log_type === UserLogType.SIGNIN) {
      logAlert.alert_event_id = 6;
      logAlert.alert_event_name = 'User Sign In';
    } else if (logUserDevice.user_log_type === UserLogType.SIGNOUT) {
      logAlert.alert_event_id = 7;
      logAlert.alert_event_name = 'User Sign Out';
    } else if (logUserDevice.user_log_type === UserLogType.FORCE_SIGNOUT) {
      logAlert.alert_event_id = 7;
      logAlert.alert_event_name = 'User Force Sign Out';
    }

    logAlert.log_id = logUserDevice.id;
    logAlert.log_uuid = logUserDevice.uuid;
    logAlert.reference_name = `User ${logUserDevice.user_log_type}`;
    logAlert.parent_branch_id = user.parent_branch_id;
    logAlert.branch_id = user.parent_branch_id;
    logAlert.user_id = user.id;
    logAlert.role_id = user.role_id;
    logAlert.payload_data = {
      type: 'user',
      user: user,
      logUserDevice: logUserDevice,
    };
    logAlert.event_time = logUserDevice.event_time;
    logAlert.deleted_on_dashboard = false;
    logAlert.original_submitted_time = logUserDevice.event_time;

    await this.logAlertRepository.save(logAlert);

    return logAlert;
  }
}
