import { Module } from '@nestjs/common';
import { SignInOutAnalyticService } from './services/sign-in-out-analytic.service';
import { SignInOutLogGenerateDocumentService } from './services/sign-in-out-log-generate-document.service';
import { SignInOutLogAlertService } from './services/sign-in-out-log-alert.service';

@Module({
  imports: [],
  controllers: [],
  providers: [SignInOutAnalyticService, SignInOutLogGenerateDocumentService, SignInOutLogAlertService],
  exports: [SignInOutAnalyticService, SignInOutLogGenerateDocumentService, SignInOutLogAlertService],
})
export class SignInOutAnalyticModule {}
