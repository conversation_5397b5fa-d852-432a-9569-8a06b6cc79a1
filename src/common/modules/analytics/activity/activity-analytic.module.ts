import { Module } from '@nestjs/common';
import { ActivityLogAnalyticService } from './services/activity-log-analytic.service';
import { ActivityLogGenerateDocumentService } from './services/activity-log-generate-document.service';
import { ActivityLogAlertService } from './services/activity-log-alert.service';

@Module({
  imports: [],
  controllers: [],
  providers: [
    ActivityLogAnalyticService,
    ActivityLogGenerateDocumentService,
    ActivityLogAlertService,
  ],
  exports: [
    ActivityLogAnalyticService,
    ActivityLogGenerateDocumentService,
    ActivityLogAlertService,
  ],
})
export class ActivityAnalyticModule {}
