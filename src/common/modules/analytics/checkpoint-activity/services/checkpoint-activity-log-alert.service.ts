import { Injectable } from '@nestjs/common';
import { Checkpoint } from '../../../database/entities/checkpoint.entity';
import { LogCheckpoint } from '../../../database/entities/log-checkpoint.entity';
import { User } from '../../../database/entities/user.entity';
import { LogAlert } from '../../../database/entities/log-alert.entity';
import { v4 as uuidv4 } from 'uuid';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class CheckpointActivityLogAlertService {
  constructor(
    @InjectRepository(LogAlert)
    private readonly logAlertRepository: Repository<LogAlert>,
  ) {}

  public async createLogAlert(
    logCheckpoint: LogCheckpoint,
  ) {
    // Check if logCheckpoint already has a logAlert
    const existingLogAlert = await this.logAlertRepository.findOne({
      where: { log_id: logCheckpoint.id },
    });

    if (existingLogAlert) {
      return existingLogAlert;
    }

    const logAlert = new LogAlert();

    logAlert.uuid = uuidv4();
    logAlert.alert_event_id = 5;
    logAlert.alert_event_name = 'Checkpoint Scanned';
    logAlert.log_id = logCheckpoint.id;
    logAlert.log_uuid = logCheckpoint.uuid;
    logAlert.reference_name = logCheckpoint.checkpoint_name;
    logAlert.parent_branch_id = logCheckpoint.parent_branch_id;
    logAlert.branch_id = logCheckpoint.branch_id;
    logAlert.user_id = logCheckpoint.user_id;
    logAlert.role_id = logCheckpoint.role_id;
    logAlert.payload_data = {
      type: 'checkpoint',
      logCheckpoint: logCheckpoint,
    };
    logAlert.event_time = logCheckpoint.event_time;
    logAlert.deleted_on_dashboard = false;
    logAlert.original_submitted_time = logCheckpoint.original_submitted_time;

    await this.logAlertRepository.save(logAlert);

    return logAlert;
  }
}
