import { Injectable } from '@nestjs/common';
import * as PDFDocument from 'pdfkit';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import axios from 'axios';
import * as ExcelJS from 'exceljs';
import { LogCheckpoint } from '../../../database/entities/log-checkpoint.entity';
import { Branch } from '../../../database/entities/branch.entity';
import { Role } from '../../../database/entities/role.entity';
import { User } from '../../../database/entities/user.entity';
import { Label } from '../../../database/entities/label.entity';
import { Device } from '../../../database/entities/device.entity';
import { Zone } from '../../../database/entities/zone.entity';
import { Checkpoint } from '../../../database/entities/checkpoint.entity';
import { Timezone } from '../../../database/entities/timezone.entity';

dayjs.extend(timezone);

export interface CheckpointActivityLogFilters {
  startDate: string | null;
  endDate: string | null;
  startTime: string | null;
  endTime: string | null;
  branch: Branch | null;
  role: Role | null;
  user: User | null;
  user_labels: Label[];
  device: Device | null;
  device_labels: Label[];
  zone: Zone | null;
  zone_labels: Label[];
  checkpoint: Checkpoint | null;
  checkpoint_labels: Label[];
}

@Injectable()
export class CheckpointActivityLogGenerateDocumentService {
  constructor() {}

  async generatePDFById(
    activityLog: LogCheckpoint,
    timezone: Timezone,
  ): Promise<{
    buffer: Buffer;
    filename: string;
  }> {
    // Create a new PDF document with custom options
    const doc = this.createPdfDoc();
    // Create a buffer to store the PDF
    const buffers: Buffer[] = [];
    doc.on('data', buffers.push.bind(buffers));

    // Helper functions for PDF formatting
    const drawSectionHeader = (text: string, y: number) => {
      doc
        .fontSize(16)
        .font('Helvetica-Bold')
        .fillColor('#1a237e')
        .text(text, 50, y);

      doc
        .moveTo(50, y + 25)
        .lineTo(545, y + 25)
        .lineWidth(1)
        .strokeColor('#1a237e')
        .stroke();
    };

    const addFieldRow = (
      label: string,
      value: string,
      x: number,
      y: number,
      rowHeight: number = 25,
    ) => {
      const labelY = y + (rowHeight - doc.currentLineHeight()) / 2;
      const valueY = y + (rowHeight - doc.currentLineHeight()) / 2;

      doc
        .font('Helvetica-Bold')
        .fontSize(10)
        .fillColor('#546e7a')
        .text(label, x, labelY);

      doc
        .font('Helvetica')
        .fontSize(10)
        .fillColor('#263238')
        .text(value || '-', x + 90, valueY);

      return y + rowHeight;
    };

    // Add Page
    doc.addPage();

    // Add company logo and name in header
    try {
      const response = await axios.get(
        'https://trial-serverless.web.app/logo.png',
        {
          responseType: 'arraybuffer',
        },
      );
      const logoBuffer = Buffer.from(response.data, 'binary');

      // Then add the image
      doc.image(logoBuffer, 50, 50, {
        fit: [50, 50],
      });
    } catch (error) {
      console.error('Error loading logo:', error);
      // Fallback if logo loading fails
      doc.save().translate(75, 75).rect(-25, -25, 50, 50).fill('#1a237e');
    }

    // Add company name next to logo
    doc
      .font('Helvetica-Bold')
      .fontSize(20)
      .fillColor('#1a237e')
      .text('UNIGUARD', 110, 65);

    // Add generation time in header
    doc
      .font('Helvetica')
      .fontSize(10)
      .fillColor('#546e7a')
      .text(
        `Generated: ${dayjs().tz(timezone.timezone_name).format('YYYY-MM-DD HH:mm')}`,
        350,
        65,
      );

    // Add title and header
    drawSectionHeader('Checkpoint Activity Log Details', 120);

    // Add UUID right after header
    doc
      .font('Helvetica')
      .fontSize(10)
      .fillColor('#546e7a')
      .text(`UUID: ${activityLog.uuid}`, 50, 155);

    // Add horizontal line after UUID
    doc
      .moveTo(50, 170)
      .lineTo(545, 170)
      .lineWidth(0.5)
      .strokeColor('#e0e0e0')
      .stroke();

    // Add entry details with improved layout
    let currentY = 190;

    // Create dayjs object with timezone for reuse
    const originalSubmittedTime = dayjs(activityLog.original_submitted_time).tz(
      activityLog.timezone_name,
    );

    // Create a table-like structure for the data
    const leftColumnData = [
      { label: 'Branch', value: activityLog.branch_name },
      { label: 'Role', value: activityLog.role_name },
      { label: 'User', value: activityLog.user_name },
      { label: 'Device', value: activityLog.device_name },
      { label: 'Checkpoint', value: activityLog.checkpoint_name },
      { label: 'Zone', value: activityLog.zone_name },
      { label: 'Timezone', value: activityLog.timezone_name },
    ];

    const rightColumnData = [
      { label: 'Latitude', value: activityLog.latitude?.toString() },
      { label: 'Longitude', value: activityLog.longitude?.toString() },
      {
        label: 'Original Time',
        value: originalSubmittedTime.format('YYYY-MM-DD HH:mm'),
      },
      { label: 'Is Beacon', value: activityLog.is_beacon ? 'Yes' : 'No' },
      { label: 'Serial Number', value: activityLog.serial_number },
      { label: 'Major Value', value: activityLog.major_value?.toString() },
      { label: 'Minor Value', value: activityLog.minor_value?.toString() },
    ];

    // Draw table content with alternating background and vertical centering
    leftColumnData.forEach((item, i) => {
      const rowHeight = 30; // Increased row height for better spacing

      // Draw alternating background
      if (i % 2 === 0) {
        doc.fillColor('#fafafa').rect(50, currentY, 495, rowHeight).fill();
      }

      // Draw left column data
      currentY = addFieldRow(
        item.label + ':',
        item.value,
        50,
        currentY,
        rowHeight,
      );

      // Draw right column data if available
      if (i < rightColumnData.length) {
        addFieldRow(
          rightColumnData[i].label + ':',
          rightColumnData[i].value,
          300,
          currentY - rowHeight,
          rowHeight,
        );
      }
    });

    // Add footer
    const footerText = 'UniGuard Security System';
    const footerWidth = doc.widthOfString(footerText);
    doc
      .fontSize(10)
      .fillColor('#9e9e9e')
      .text(
        footerText,
        (doc.page.width - footerWidth) / 2,
        doc.page.height - 50,
      );

    // Finalize the PDF
    doc.end();

    return new Promise(resolve => {
      doc.on('end', () => {
        const buffer = Buffer.concat(buffers);
        resolve({
          buffer: buffer,
          filename: `checkpoint-activity-log-${dayjs().tz(timezone.timezone_name).format('YYYYMMDD-HHmmss')}.pdf`,
        });
      });
    });
  }

  async generateSpreadsheet(
    data: LogCheckpoint[],
    filters: CheckpointActivityLogFilters,
    timezone: Timezone,
  ): Promise<{ buffer: Buffer; filename: string }> {
    // Create a new workbook and worksheet
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Checkpoint Logs');
    let currentRow = 1;

    // Add title and styling (merged across all columns)
    worksheet.mergeCells('A1:O1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = 'UNIGUARD CHECKPOINT LOGS REPORT';
    titleCell.font = { size: 16, bold: true, color: { argb: '1A237E' } };
    titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getRow(1).height = 30;
    currentRow++;

    // Add generation time and total entries
    worksheet.mergeCells(`A${currentRow}:O${currentRow}`);
    const timeCell = worksheet.getCell(`A${currentRow}`);
    timeCell.value = `Generated: ${dayjs().tz(timezone?.timezone_name).format('YYYY-MM-DD HH:mm')} | Total Entries: ${data.length}`;
    timeCell.font = { size: 10, color: { argb: '546E7A' } };
    timeCell.alignment = { horizontal: 'center' };

    // Add filter information
    currentRow++;
    worksheet.mergeCells(`A${currentRow}:O${currentRow}`);
    const filterTitleCell = worksheet.getCell(`A${currentRow}`);
    filterTitleCell.value = 'FILTER CRITERIA';
    filterTitleCell.font = { size: 12, bold: true, color: { argb: '1A237E' } };

    // Add filter details
    if (filters.startDate && filters.endDate) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Date Range:';
      worksheet.getCell(`B${currentRow}`).value =
        `${filters.startDate} to ${filters.endDate}`;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.startTime && filters.endTime) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Time Range:';
      worksheet.getCell(`B${currentRow}`).value =
        `${filters.startTime} to ${filters.endTime}`;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.branch) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Branch:';
      worksheet.getCell(`B${currentRow}`).value = filters.branch.branch_name;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.role) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Role:';
      worksheet.getCell(`B${currentRow}`).value = filters.role.role_name;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.user) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'User:';
      worksheet.getCell(`B${currentRow}`).value = filters.user.name;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    } else if (filters.user_labels.length > 0) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'User Labels:';
      worksheet.getCell(`B${currentRow}`).value = filters.user_labels
        .map(label => label.label_name)
        .join(', ');
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.device) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Device:';
      worksheet.getCell(`B${currentRow}`).value = filters.device.device_name;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    } else if (filters.device_labels.length > 0) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Device Labels:';
      worksheet.getCell(`B${currentRow}`).value = filters.device_labels
        .map(label => label.label_name)
        .join(', ');
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.checkpoint) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Checkpoint:';
      worksheet.getCell(`B${currentRow}`).value =
        filters.checkpoint.checkpoint_name;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    } else if (filters.checkpoint_labels.length > 0) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Checkpoint Labels:';
      worksheet.getCell(`B${currentRow}`).value = filters.checkpoint_labels
        .map(label => label.label_name)
        .join(', ');
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.zone) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Zone:';
      worksheet.getCell(`B${currentRow}`).value = filters.zone.zone_name;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    } else if (filters.zone_labels.length > 0) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Zone Labels:';
      worksheet.getCell(`B${currentRow}`).value = filters.zone_labels
        .map(label => label.label_name)
        .join(', ');
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    // Add date range explanation section
    if (filters.startDate && filters.endDate) {
      currentRow += 2;
      let startRow = currentRow;
      let endRow = currentRow;
      const cellDateRange = worksheet.getCell(`A${currentRow}`);
      cellDateRange.value = 'Date Range:';
      cellDateRange.font = { bold: true };
      cellDateRange.alignment = { horizontal: 'left', vertical: 'middle' };

      // Calculate the number of days between the start and end date
      const startDate = dayjs(filters.startDate);
      const endDate = dayjs(filters.endDate);
      const daysDiff = endDate.diff(startDate, 'day') + 1;
      const startTime = filters.startTime || '00:00';
      const endTime = filters.endTime || '23:59';

      const maxDaysToShow = 3;
      const daysToShow = Math.min(daysDiff, maxDaysToShow);

      for (let i = 0; i < daysToShow; i++) {
        if (i > 0) {
          currentRow++;
        }
        endRow = currentRow;

        const currentDate = startDate.add(i, 'day');
        const dateStr = currentDate.format('YYYY-MM-DD');
        worksheet.getCell(`B${currentRow}`).value =
          `${dateStr} ${startTime} until ${dateStr} ${endTime}`;
      }

      if (daysDiff > maxDaysToShow) {
        currentRow++;
        worksheet.getCell(`B${currentRow}`).value = '...';

        // Show last day
        currentRow++;
        endRow = currentRow;
        const dateStr = endDate.format('YYYY-MM-DD');
        worksheet.getCell(`B${currentRow}`).value =
          `${dateStr} ${startTime} until ${dateStr} ${endTime}`;
      }

      // Merge the date range cells
      worksheet.mergeCells(`A${startRow}:A${endRow}`);
    }

    // Define headers
    const headers = [
      'UUID',
      'Branch',
      'Role',
      'User',
      'Device',
      'Checkpoint',
      'Zone',
      'Timezone',
      'Latitude',
      'Longitude',
      'Is Beacon',
      'Serial Number',
      'Major Value',
      'Minor Value',
      'Original Time',
    ];

    // Add header row with styling
    currentRow += 2;
    worksheet.addRow(2);
    const headerRow = worksheet.addRow(headers);
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '1A237E' },
    };
    headerRow.alignment = { horizontal: 'center' };

    // Add data rows
    currentRow++;
    data.forEach((log, index) => {
      // Create dayjs object with timezone for reuse
      const originalSubmittedTime = dayjs(log.original_submitted_time).tz(
        log.timezone_name,
      );

      const row = worksheet.addRow([
        log.uuid,
        log.branch?.branch_name || '-',
        log.role?.role_name || '-',
        log.user?.name || '-',
        log.device?.device_name || '-',
        log.checkpoint?.checkpoint_name || '-',
        log.zone?.zone_name || '-',
        log.timezone?.timezone_name || '-',
        log.latitude?.toString() || '-',
        log.longitude?.toString() || '-',
        log.is_beacon ? 'Yes' : 'No',
        log.serial_number || '-',
        log.major_value?.toString() || '-',
        log.minor_value?.toString() || '-',
        originalSubmittedTime.format('YYYY-MM-DD HH:mm') || '-',
      ]);

      // Add alternating row colors
      if (index % 2 === 1) {
        row.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'F5F5F5' },
        };
      }

      currentRow++;
    });

    // Set column widths
    headers.forEach((_, index) => {
      worksheet.getColumn(index + 1).width = 20;
    });

    // Add footer
    currentRow += 2;
    worksheet.mergeCells(`A${currentRow}:O${currentRow}`);
    const footerCell = worksheet.getCell(`A${currentRow}`);
    footerCell.value = 'UniGuard Security System';
    footerCell.font = { size: 10, color: { argb: '9E9E9E' } };
    footerCell.alignment = { horizontal: 'center' };

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();

    return {
      buffer: buffer as Buffer,
      filename: `checkpoint-activity-logs-${dayjs().tz(timezone.timezone_name).format('YYYYMMDD-HHmmss')}.xlsx`,
    };
  }

  async generatePDF(
    data: LogCheckpoint[],
    filters: CheckpointActivityLogFilters,
    timezone: Timezone,
  ): Promise<{ buffer: Buffer; filename: string }> {
    // Create a new PDF document with custom options
    const doc = this.createPdfDoc();
    // Create a buffer to store the PDF
    const buffers: Buffer[] = [];
    doc.on('data', buffers.push.bind(buffers));

    // Helper function to create section headers
    const drawSectionHeader = (text: string, y: number) => {
      doc
        .fontSize(16)
        .font('Helvetica-Bold')
        .fillColor('#1a237e')
        .text(text, 50, y);

      doc
        .moveTo(50, y + 25)
        .lineTo(545, y + 25)
        .lineWidth(1)
        .strokeColor('#1a237e')
        .stroke();
    };

    // Helper function to add field rows with vertical centering
    const addFieldRow = (
      label: string,
      value: string,
      x: number,
      y: number,
      rowHeight: number = 25,
    ) => {
      const labelY = y + (rowHeight - doc.currentLineHeight()) / 2;
      const valueY = y + (rowHeight - doc.currentLineHeight()) / 2;

      doc
        .font('Helvetica-Bold')
        .fontSize(10)
        .fillColor('#546e7a')
        .text(label, x, labelY);

      doc
        .font('Helvetica')
        .fontSize(10)
        .fillColor('#263238')
        .text(value || '-', x + 90, valueY);

      return y + rowHeight;
    };

    // Add cover page
    doc.addPage();

    // Add background
    doc.rect(0, 0, doc.page.width, doc.page.height).fill('#f5f5f5');

    // Add company logo and name
    try {
      const response = await axios.get(
        'https://trial-serverless.web.app/logo.png',
        {
          responseType: 'arraybuffer',
        },
      );
      const logoBuffer = Buffer.from(response.data, 'binary');

      // Add Logo
      doc.image(logoBuffer, (doc.page.width - 80) / 2, 100, {
        fit: [80, 80],
      });
    } catch (error) {
      // Fallback if logo loading fails
      doc
        .save()
        .translate(doc.page.width / 2, 140)
        .rect(-40, -40, 80, 80)
        .fill('#1a237e');
    }

    // Add company name
    doc
      .font('Helvetica-Bold')
      .fontSize(18)
      .fillColor('#1a237e')
      .text(
        'UNIGUARD',
        (doc.page.width - doc.widthOfString('UNIGUARD')) / 2,
        190,
      );

    // Add title
    doc.font('Helvetica-Bold').fontSize(18).fillColor('#1a237e');

    const titleWidth = doc.widthOfString('CHECKPOINT ACTIVITY LOG REPORT');
    doc.text(
      'CHECKPOINT ACTIVITY LOG REPORT',
      (doc.page.width - titleWidth) / 2,
      240,
    );

    // Add metadata section
    const metadataY = 300;
    doc.font('Helvetica').fontSize(12).fillColor('#546e7a');

    // Add active filters section
    doc
      .font('Helvetica-Bold')
      .fontSize(14)
      .fillColor('#1a237e')
      .text('Active Filters', 50, metadataY);

    doc
      .moveTo(50, metadataY + 25)
      .lineTo(545, metadataY + 25)
      .lineWidth(1)
      .strokeColor('#1a237e')
      .stroke();

    let filterY = metadataY + 30;

    // Display active filters in a clean table format
    const leftFilters = [
      {
        key: 'startDate',
        label: 'Start Date:',
        valueFunc: () =>
          filters.startDate
            ? `${dayjs(filters.startDate).format('MMMM D, YYYY')}`
            : 'All Dates',
      },
      {
        key: 'endDate',
        label: 'End Date:',
        valueFunc: () =>
          filters.endDate
            ? `${dayjs(filters.endDate).format('MMMM D, YYYY')}`
            : 'All Dates',
      },
      {
        key: 'branch',
        label: 'Branch:',
        valueFunc: () =>
          filters.branch ? `${filters.branch.branch_name}` : 'All Branches',
      },
      {
        key: 'user',
        label: 'User:',
        valueFunc: () => (filters.user ? `${filters.user.name}` : 'All Users'),
      },
      {
        key: 'device',
        label: 'Device:',
        valueFunc: () =>
          filters.device ? `${filters.device.device_name}` : 'All Devices',
      },
      {
        key: 'checkpoint',
        label: 'Checkpoint:',
        valueFunc: () =>
          filters.checkpoint
            ? `${filters.checkpoint.checkpoint_name}`
            : 'All Checkpoints',
      },
      {
        key: 'zone',
        label: 'Zone:',
        valueFunc: () =>
          filters.zone ? `${filters.zone.zone_name}` : 'All Zones',
      },
    ];

    const rightFilters = [
      {
        key: 'startTime',
        label: 'Start Time:',
        valueFunc: () =>
          filters.startTime ? `${filters.startTime}` : 'All Times',
      },
      {
        key: 'endTime',
        label: 'End Time:',
        valueFunc: () => (filters.endTime ? `${filters.endTime}` : 'All Times'),
      },
      {
        key: 'role',
        label: 'Role:',
        valueFunc: () =>
          filters.role ? `${filters.role.role_name}` : 'All Roles',
      },
      {
        key: 'user_labels',
        label: 'User Labels:',
        valueFunc: () =>
          filters.user_labels.map(label => label.label_name).join(', '),
      },
      {
        key: 'device_labels',
        label: 'Device Labels:',
        valueFunc: () =>
          filters.device_labels.map(label => label.label_name).join(', '),
      },
      {
        key: 'checkpoint_labels',
        label: 'Checkpoint Labels:',
        valueFunc: () =>
          filters.checkpoint_labels.map(label => label.label_name).join(', '),
      },
      {
        key: 'zone_labels',
        label: 'Zone Labels:',
        valueFunc: () =>
          filters.zone_labels.map(label => label.label_name).join(', '),
      },
    ];

    // Render filters in two columns
    const rowHeight = 25;
    const leftX = 50;
    const rightX = 300;

    const maxRows = Math.max(leftFilters.length, rightFilters.length);
    for (let i = 0; i < maxRows; i++) {
      // Add left column filter
      if (i < leftFilters.length) {
        const leftFilter = leftFilters[i];
        addFieldRow(
          leftFilter.label,
          leftFilter.valueFunc(),
          leftX,
          filterY,
          rowHeight,
        );
      }

      // Add right column filter
      if (i < rightFilters.length) {
        const rightFilter = rightFilters[i];
        addFieldRow(
          rightFilter.label,
          rightFilter.valueFunc(),
          rightX,
          filterY,
          rowHeight,
        );
      }

      // Add a line break after each row
      filterY += rowHeight;
    }

    // Add generation info
    let currentY = filterY;
    currentY = addFieldRow(
      'Report Generated:',
      dayjs().tz(timezone.timezone_name).format('YYYY-MM-DD HH:mm'),
      50,
      currentY,
    );
    currentY = addFieldRow(
      'Total Entries:',
      data.length.toString(),
      50,
      currentY,
    );

    // Add date range explanation section
    if (filters.startDate && filters.endDate) {
      filterY += 20;
      doc
        .font('Helvetica-Bold')
        .fontSize(14)
        .fillColor('#1a237e')
        .text('Data From:', 50, filterY);

      filterY += 25;

      // Calculate all days between start and end date
      const startDate = dayjs(filters.startDate);
      const endDate = dayjs(filters.endDate);
      const daysDiff = endDate.diff(startDate, 'day') + 1;
      const startTime = filters.startTime || '00:00';
      const endTime = filters.endTime || '23:59';

      // Display up to 5 days with truncation if more
      const maxDaysToShow = 3;
      const daysToShow = Math.min(daysDiff, maxDaysToShow);

      for (let i = 0; i < daysToShow; i++) {
        const currentDate = startDate.add(i, 'day');
        const dateStr = currentDate.format('YYYY-MM-DD');
        const timeRangeStr = `${dateStr} ${startTime} until ${dateStr} ${endTime}`;

        doc
          .font('Helvetica')
          .fontSize(10)
          .fillColor('#263238')
          .text(timeRangeStr, 50, filterY + i * 20);
      }

      // Add truncation indicator if there are more days
      if (daysDiff > maxDaysToShow) {
        doc
          .font('Helvetica')
          .fontSize(10)
          .fillColor('#263238')
          .text('...', 50, filterY + maxDaysToShow * 20);

        // Show the last day
        const lastDate = endDate.format('YYYY-MM-DD');
        const lastTimeRangeStr = `${lastDate} ${startTime} until ${lastDate} ${endTime}`;

        doc
          .font('Helvetica')
          .fontSize(10)
          .fillColor('#263238')
          .text(lastTimeRangeStr, 50, filterY + (maxDaysToShow + 1) * 20);

        filterY += (maxDaysToShow + 2) * 20;
      } else {
        filterY += daysToShow * 20;
      }
    }

    // Add footer to cover page
    const footerText = 'UniGuard Security System';
    const footerWidth = doc.widthOfString(footerText);
    doc
      .fontSize(10)
      .fillColor('#9e9e9e')
      .text(
        footerText,
        (doc.page.width - footerWidth) / 2,
        doc.page.height - 100,
      );

    // Process each checkpoint activity log entry
    for (const [index, log] of data.entries()) {
      doc.addPage();

      // Add page header with UUID
      drawSectionHeader(`Checkpoint Activity Log Entry #${index + 1}`, 50);

      // Add UUID right after header
      doc
        .font('Helvetica')
        .fontSize(10)
        .fillColor('#546e7a')
        .text(`UUID: ${log.uuid}`, 50, 85);

      // Add horizontal line after UUID
      doc
        .moveTo(50, 100)
        .lineTo(545, 100)
        .lineWidth(0.5)
        .strokeColor('#e0e0e0')
        .stroke();

      // Add entry details with improved layout
      let currentY = 120;

      // Create dayjs object with timezone for reuse
      const originalSubmittedTime = dayjs(log.original_submitted_time).tz(
        log.timezone_name,
      );

      // Create a table-like structure for the data
      const leftColumnData = [
        { label: 'Branch', value: log.branch?.branch_name },
        { label: 'Role', value: log.role?.role_name },
        { label: 'User', value: log.user?.name },
        { label: 'Device', value: log.device?.device_name },
        { label: 'Checkpoint', value: log.checkpoint?.checkpoint_name },
        { label: 'Zone', value: log.zone?.zone_name },
        { label: 'Timezone', value: log.timezone?.timezone_name },
      ];

      const rightColumnData = [
        { label: 'Latitude', value: log.latitude?.toString() },
        { label: 'Longitude', value: log.longitude?.toString() },
        {
          label: 'Original Time',
          value: originalSubmittedTime.format('YYYY-MM-DD HH:mm'),
        },
        { label: 'Is Beacon', value: log.is_beacon ? 'Yes' : 'No' },
        { label: 'Serial Number', value: log.serial_number },
        { label: 'Major Value', value: log.major_value?.toString() },
        { label: 'Minor Value', value: log.minor_value?.toString() },
      ];

      // Draw table content with alternating background and vertical centering
      leftColumnData.forEach((item, i) => {
        const rowHeight = 30; // Increased row height for better spacing

        // Draw alternating background
        if (i % 2 === 0) {
          doc.fillColor('#fafafa').rect(50, currentY, 495, rowHeight).fill();
        }

        // Draw left column data
        currentY = addFieldRow(
          item.label + ':',
          item.value,
          50,
          currentY,
          rowHeight,
        );

        // Draw right column data if available
        if (i < rightColumnData.length) {
          addFieldRow(
            rightColumnData[i].label + ':',
            rightColumnData[i].value,
            300,
            currentY - rowHeight,
            rowHeight,
          );
        }
      });

      // Add page number at fixed position from bottom
      const pageText = `Page ${index + 2} of ${data.length + 1}`;
      const pageWidth = doc.widthOfString(pageText);
      doc
        .font('Helvetica')
        .fontSize(10)
        .fillColor('#9e9e9e')
        .text(pageText, (doc.page.width - pageWidth) / 2, doc.page.height - 30);
    }

    // Finalize the PDF
    doc.end();

    return new Promise(resolve => {
      doc.on('end', () => {
        const buffer = Buffer.concat(buffers);
        resolve({
          buffer: buffer,
          filename: `checkpoint-activity-logs-${dayjs().tz(timezone.timezone_name).format('YYYYMMDD-HHmmss')}.pdf`,
        });
      });
    });
  }

  async generateSpreadsheetById(
    activityLog: LogCheckpoint,
    timezone: Timezone,
  ): Promise<{
    buffer: Buffer;
    filename: string;
  }> {
    let currentRow = 1;
    // Create a new workbook and worksheet
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Checkpoint Log');

    // Add title and styling
    worksheet.mergeCells('A1:G1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = 'UNIGUARD CHECKPOINT LOG REPORT';
    titleCell.font = { size: 16, bold: true, color: { argb: '1A237E' } };
    titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getRow(1).height = 30;
    currentRow++;

    // Add generation time
    worksheet.mergeCells('A2:G2');
    const timeCell = worksheet.getCell('A2');
    timeCell.value = `Generated: ${dayjs().tz(timezone.timezone_name).format('YYYY-MM-DD HH:mm')}`;
    timeCell.font = { size: 10, color: { argb: '546E7A' } };
    timeCell.alignment = { horizontal: 'center' };
    currentRow++;

    // Add UUID
    worksheet.mergeCells('A3:G3');
    const uuidCell = worksheet.getCell('A3');
    uuidCell.value = `UUID: ${activityLog.uuid}`;
    uuidCell.font = { size: 10, color: { argb: '546E7A' } };
    currentRow++;

    // Add header row
    const headerRow = worksheet.addRow(['Field', 'Value']);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'E3F2FD' },
    };
    currentRow++;

    // Create dayjs object with timezone for reuse
    const originalSubmittedTime = dayjs(activityLog.original_submitted_time).tz(
      activityLog.timezone_name,
    );

    // Add data rows
    const dataRows = [
      ['Branch', activityLog.branch_name || '-'],
      ['Role', activityLog.role_name || '-'],
      ['User', activityLog.user_name || '-'],
      ['Device', activityLog.device_name || '-'],
      ['Checkpoint', activityLog.checkpoint_name || '-'],
      ['Zone', activityLog.zone_name || '-'],
      ['Timezone', activityLog.timezone_name || '-'],
      ['Latitude', activityLog.latitude?.toString() || '-'],
      ['Longitude', activityLog.longitude?.toString() || '-'],
      ['Original Time', originalSubmittedTime.format('YYYY-MM-DD HH:mm')],
      ['Is Beacon', activityLog.is_beacon ? 'Yes' : 'No'],
      ['Serial Number', activityLog.serial_number || '-'],
      ['Major Value', activityLog.major_value?.toString() || '-'],
      ['Minor Value', activityLog.minor_value?.toString() || '-'],
    ];

    // Add data rows with alternating colors
    dataRows.forEach((row, index) => {
      currentRow++;
      const excelRow = worksheet.addRow(row);
      // Add alternating row colors for better readability
      if (index % 2 === 1) {
        excelRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FAFAFA' },
        };
      }
    });

    // Set column widths for better layout
    worksheet.getColumn(1).width = 20;
    worksheet.getColumn(2).width = 50;

    // Add footer with system information
    currentRow += 2;
    const footerRowIndex = currentRow;
    worksheet.mergeCells(`A${footerRowIndex}:G${footerRowIndex}`);
    const footerCell = worksheet.getCell(`A${footerRowIndex}`);
    footerCell.value = 'UniGuard Security System';
    footerCell.font = { size: 10, color: { argb: '9E9E9E' } };
    footerCell.alignment = { horizontal: 'center' };

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();

    return {
      buffer: buffer as Buffer,
      filename: `checkpoint-activity-log-${activityLog.id}-${dayjs().tz(timezone.timezone_name).format('YYYYMMDD-HHmmss')}.xlsx`,
    };
  }

  private createPdfDoc = () => {
    return new PDFDocument({
      size: 'A4',
      margins: {
        top: 10,
        bottom: 10,
        left: 10,
        right: 10,
      },
      autoFirstPage: false,
      info: {
        Title: 'Checkpoint Activity Log',
        Author: 'UniGuard',
        Creator: 'UniGuard',
      },
    });
  };
}
