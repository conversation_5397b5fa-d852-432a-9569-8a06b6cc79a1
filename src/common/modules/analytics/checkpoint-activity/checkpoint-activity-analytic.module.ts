import { Module } from '@nestjs/common';
import { CheckpointActivityAnalyticService } from './services/checkpoint-activity-analytic.service';
import { CheckpointActivityLogGenerateDocumentService } from './services/checkpoint-activity-log-generate-document.service';
import { CheckpointActivityLogAlertService } from './services/checkpoint-activity-log-alert.service';

@Module({
  imports: [],
  controllers: [],
  providers: [
    CheckpointActivityAnalyticService,
    CheckpointActivityLogGenerateDocumentService,
    CheckpointActivityLogAlertService,
  ],
  exports: [
    CheckpointActivityAnalyticService,
    CheckpointActivityLogGenerateDocumentService,
    CheckpointActivityLogAlertService,
  ],
})
export class CheckpointActivityAnalyticModule {}
