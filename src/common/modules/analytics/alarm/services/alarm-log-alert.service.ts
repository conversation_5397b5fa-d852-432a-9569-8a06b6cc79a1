import { Injectable } from '@nestjs/common';
import { LogAlarm } from '../../../database/entities/log-alarm.entity';
import { User } from '../../../database/entities/user.entity';
import { LogAlert } from '../../../database/entities/log-alert.entity';
import { v4 as uuidv4 } from 'uuid';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class AlarmLogAlertService {
  constructor(
    @InjectRepository(LogAlert)
    private readonly logAlertRepository: Repository<LogAlert>,
  ) {}

  public async createLogAlert(
    logAlarm: LogAlarm,
  ) {
    // Check if logAlarm already has a logAlert
    const existingLogAlert = await this.logAlertRepository.findOne({
      where: { log_id: logAlarm.id },
    });

    if (existingLogAlert) {
      return existingLogAlert;
    }

    const logAlert = new LogAlert();

    logAlert.uuid = uuidv4();
    logAlert.alert_event_id = 4;
    logAlert.alert_event_name = 'Alarm Started';
    logAlert.log_id = logAlarm.id;
    logAlert.log_uuid = logAlarm.uuid;
    logAlert.reference_name = 'Alarm';
    logAlert.parent_branch_id = logAlarm.parent_branch_id;
    logAlert.branch_id = logAlarm.branch_id;
    logAlert.user_id = logAlarm.user_id;
    logAlert.role_id = logAlarm.role_id;
    logAlert.payload_data = {
      type: 'alarm',
      logAlarm: logAlarm,
    };
    logAlert.event_time = logAlarm.event_time;
    logAlert.deleted_on_dashboard = false;
    logAlert.original_submitted_time = logAlarm.original_submitted_time;

    await this.logAlertRepository.save(logAlert);

    return logAlert;
  }
}
