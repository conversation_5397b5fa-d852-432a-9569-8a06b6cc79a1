import { Module } from '@nestjs/common';
import { AlarmAnalyticService } from './services/alarm-analytic.service';
import { AlarmLogGenerateDocumentService } from './services/alarm-log-generate-document.service';
import { AlarmLogAlertService } from './services/alarm-log-alert.service';

@Module({
  imports: [],
  controllers: [],
  providers: [AlarmAnalyticService, AlarmLogGenerateDocumentService, AlarmLogAlertService],
  exports: [AlarmAnalyticService, AlarmLogGenerateDocumentService, AlarmLogAlertService],
})
export class AlarmAnalyticModule {}
