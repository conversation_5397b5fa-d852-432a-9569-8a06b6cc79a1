import { Module } from '@nestjs/common';
import { TaskAnalyticService } from './services/task-analytic.service';
import { TaskLogGenerateDocumentService } from './services/task-log-generate-document.service';
import { TaskLogAlertService } from './services/task-log-alert.service';

@Module({
  imports: [],
  controllers: [],
  providers: [TaskAnalyticService, TaskLogGenerateDocumentService, TaskLogAlertService],
  exports: [TaskAnalyticService, TaskLogGenerateDocumentService, TaskLogAlertService],
})
export class TaskAnalyticModule {}
