import { Injectable } from '@nestjs/common';
import { Task } from '../../../database/entities/task.entity';
import { LogTask } from '../../../database/entities/log-task.entity';
import { User } from '../../../database/entities/user.entity';
import { LogAlert } from '../../../database/entities/log-alert.entity';
import { v4 as uuidv4 } from 'uuid';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class TaskLogAlertService {
  constructor(
    @InjectRepository(LogAlert)
    private readonly logAlertRepository: Repository<LogAlert>,
  ) {}

  public async createLogAlert(
    logTask: LogTask,
  ) {
    // Check if logTask already has a logAlert
    const existingLogAlert = await this.logAlertRepository.findOne({
      where: { log_id: logTask.id },
    });

    if (existingLogAlert) {
      return existingLogAlert;
    }

    const logAlert = new LogAlert();

    logAlert.uuid = uuidv4();
    logAlert.alert_event_id = 2;
    logAlert.alert_event_name = 'Task Submitted';
    logAlert.log_id = logTask.id;
    logAlert.log_uuid = logTask.uuid;
    logAlert.reference_name = logTask.task_name;
    logAlert.parent_branch_id = logTask.parent_branch_id;
    logAlert.branch_id = logTask.branch_id;
    logAlert.user_id = logTask.user_id;
    logAlert.role_id = logTask.role_id;
    logAlert.payload_data = {
      type: 'task',
      logTask: logTask,
    };
    logAlert.event_time = logTask.event_time;
    logAlert.deleted_on_dashboard = false;
    logAlert.original_submitted_time = logTask.original_submitted_time;

    await this.logAlertRepository.save(logAlert);

    return logAlert;
  }
}
