import { Module } from '@nestjs/common';
import { FormAnalyticService } from './services/form-analytic.service';
import { FormsLogGenerateDocumentService } from './services/forms-log-generate-document.service';
import { FormLogAlertService } from './services/form-log-alert.service';

@Module({
  imports: [],
  controllers: [],
  providers: [FormAnalyticService, FormsLogGenerateDocumentService, FormLogAlertService],
  exports: [FormAnalyticService, FormsLogGenerateDocumentService, FormLogAlertService],
})
export class FormAnalyticModule {}
