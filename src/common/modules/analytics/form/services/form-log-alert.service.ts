import { Injectable } from '@nestjs/common';
import { LogForm } from '../../../database/entities/log-form.entity';
import { LogAlert } from '../../../database/entities/log-alert.entity';
import { v4 as uuidv4 } from 'uuid';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class FormLogAlertService {
  constructor(
    @InjectRepository(LogAlert)
    private readonly logAlertRepository: Repository<LogAlert>,
  ) {}

  public async createLogAlert(logForm: LogForm) {
    // Check if logForm already has a logAlert
    const existingLogAlert = await this.logAlertRepository.findOne({
      where: { log_id: logForm.id },
    });

    if (existingLogAlert) {
      return existingLogAlert;
    }

    const logAlert = new LogAlert();

    logAlert.uuid = uuidv4();
    logAlert.alert_event_id = 3;
    logAlert.alert_event_name = 'Form Submitted';
    logAlert.log_id = logForm.id;
    logAlert.log_uuid = logForm.uuid;
    logAlert.reference_name = logForm.form_name;
    logAlert.parent_branch_id = logForm.parent_branch_id;
    logAlert.branch_id = logForm.branch_id;
    logAlert.user_id = logForm.user_id;
    logAlert.role_id = logForm.role_id;
    logAlert.payload_data = {
      type: 'form',
      logForm: logForm,
    };
    logAlert.event_time = logForm.event_time;
    logAlert.deleted_on_dashboard = false;
    logAlert.original_submitted_time = logForm.original_submitted_time;

    await this.logAlertRepository.save(logAlert);

    return logAlert;
  }
}
