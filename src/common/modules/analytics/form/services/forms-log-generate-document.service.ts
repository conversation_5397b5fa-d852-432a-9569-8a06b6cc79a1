import { Injectable } from '@nestjs/common';
import { Branch } from '../../../database/entities/branch.entity';
import { User } from '../../../database/entities/user.entity';
import { Role } from '../../../database/entities/role.entity';
import { Label } from '../../../database/entities/label.entity';
import { Device } from '../../../database/entities/device.entity';
import { Form } from '../../../database/entities/form.entity';
import axios from 'axios';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';

// Extend dayjs with timezone support
dayjs.extend(utc);
dayjs.extend(timezone);
import * as ExcelJS from 'exceljs';
import * as PDFDocument from 'pdfkit';
import { LogForm } from '../../../database/entities/log-form.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LogFormField } from '../../../database/entities/log-form-field.entity';
import { Timezone } from '../../../database/entities/timezone.entity';

export interface FormsLogFiltersGenerateDocument {
  startDate: string | null;
  endDate: string | null;
  startTime: string | null;
  endTime: string | null;
  branch: Branch | null;
  role: Role | null;
  user: User | null;
  user_labels: Label[];
  device: Device | null;
  device_labels: Label[];
  form: Form | null;
}

@Injectable()
export class FormsLogGenerateDocumentService {
  private createPdfDoc = () => {
    return new PDFDocument({
      size: 'A4',
      autoFirstPage: false,
      margins: {
        top: 10,
        bottom: 10,
        left: 10,
        right: 10,
      },
      info: {
        Title: 'Forms Log Report',
        Author: 'UniGuard System',
        Creator: 'UniGuard',
      },
    });
  };

  constructor(
    @InjectRepository(LogFormField)
    private formFieldRepository: Repository<LogFormField>,
  ) {}

  async generatePDFById(formLog: LogForm, timezone: Timezone): Promise<{
    buffer: Buffer;
    filename: string;
  }> {
    // Create a new PDF document with custom options
    const doc = this.createPdfDoc();
    // Create a buffer to store the PDF
    const buffers: Buffer[] = [];
    doc.on('data', buffers.push.bind(buffers));

    // Helper functions for PDF formatting
    const drawSectionHeader = (text: string, y: number) => {
      doc
        .fontSize(16)
        .font('Helvetica-Bold')
        .fillColor('#1a237e')
        .text(text, 50, y);

      doc
        .moveTo(50, y + 25)
        .lineTo(545, y + 25)
        .lineWidth(1)
        .strokeColor('#1a237e')
        .stroke();
    };

    const addFieldRow = (
      label: string,
      value: string,
      x: number,
      y: number,
      rowHeight: number = 25,
    ) => {
      const labelY = y + (rowHeight - doc.currentLineHeight()) / 2;
      const valueY = y + (rowHeight - doc.currentLineHeight()) / 2;

      doc
        .font('Helvetica-Bold')
        .fontSize(10)
        .fillColor('#546e7a')
        .text(label, x, labelY);

      doc
        .font('Helvetica')
        .fontSize(10)
        .fillColor('#263238')
        .text(value || '-', x + 150, valueY);

      return y + rowHeight;
    };

    // Add Page
    doc.addPage();

    // Add company logo and name in header
    try {
      const response = await axios.get(
        'https://trial-serverless.web.app/logo.png',
        {
          responseType: 'arraybuffer',
        },
      );
      const logoBuffer = Buffer.from(response.data, 'binary');

      // Then add the image
      doc.image(logoBuffer, 50, 50, {
        fit: [50, 50],
      });
    } catch (error) {
      console.error('Error loading logo:', error);
      // Fallback if logo loading fails
      doc.save().translate(75, 75).rect(-25, -25, 50, 50).fill('#1a237e');
    }

    // Add company name next to logo
    doc
      .font('Helvetica-Bold')
      .fontSize(20)
      .fillColor('#1a237e')
      .text('UNIGUARD', 110, 65);

    // Add generation time in header
    doc
      .font('Helvetica')
      .fontSize(10)
      .fillColor('#546e7a')
      .text(`Generated: ${dayjs().tz(timezone.timezone_name).format('YYYY-MM-DD HH:mm')}`, 350, 65);

    // Add title and header
    drawSectionHeader('Form Log Details', 120);

    // Add UUID right after header
    doc
      .font('Helvetica')
      .fontSize(10)
      .fillColor('#546e7a')
      .text(`UUID: ${formLog.uuid}`, 50, 155);

    // Add horizontal line after UUID
    doc
      .moveTo(50, 170)
      .lineTo(545, 170)
      .lineWidth(0.5)
      .strokeColor('#e0e0e0')
      .stroke();

    // Add entry details with improved layout
    let currentY = 190;

    // Create a table-like structure for the data
    const leftColumnData = [
      { label: 'Role', value: formLog.role_name },
      { label: 'User', value: formLog.user_name },
      { label: 'Form', value: formLog.form_name },
      { label: 'Device', value: formLog.device_name },
    ];

    const rightColumnData = [
      { label: 'Timezone', value: formLog.timezone_name },
      { label: 'Latitude', value: formLog.latitude?.toString() },
      { label: 'Longitude', value: formLog.longitude?.toString() },
      {
        label: 'Original Time',
        value: dayjs(formLog.original_submitted_time).tz(formLog.timezone_name).format(
          'YYYY-MM-DD HH:mm',
        ),
      },
    ];

    // Draw table content with alternating background and vertical centering
    leftColumnData.forEach((item, i) => {
      const rowHeight = 30; // Increased row height for better spacing

      // Draw alternating background
      if (i % 2 === 0) {
        doc.fillColor('#fafafa').rect(50, currentY, 495, rowHeight).fill();
      }

      // Draw left column data
      currentY = addFieldRow(
        item.label + ':',
        item.value,
        50,
        currentY,
        rowHeight,
      );

      // Draw right column data if available
      if (i < rightColumnData.length) {
        addFieldRow(
          rightColumnData[i].label + ':',
          rightColumnData[i].value,
          300,
          currentY - rowHeight,
          rowHeight,
        );
      }
    });

    // Add form fields section
    currentY += 20;
    drawSectionHeader('Form Fields', currentY);
    currentY += 40;

    // Get form fields and add them to the PDF
    const formFields = await this.formFieldRepository.find({
      where: { log_form_id: formLog.id },
      relations: ['field_type'],
    });

    for (const [index, field] of formFields.entries()) {
      const rowHeight = 30;
      if (index % 2 === 0) {
        doc.fillColor('#fafafa').rect(50, currentY, 495, rowHeight).fill();
      }

      // Handle image and signature fields differently
      if (
        field.field_type_name === 'image' ||
        field.field_type_name === 'signature'
      ) {
        // Add field name
        doc
          .font('Helvetica-Bold')
          .fontSize(10)
          .fillColor('#546e7a')
          .text(
            field.form_field_name + ':',
            50,
            currentY + (rowHeight - doc.currentLineHeight()) / 2,
          );

        try {
          // Try to load and add the image
          const response = await axios.get(field.field_type_value, {
            responseType: 'arraybuffer',
          });
          const imageBuffer = Buffer.from(response.data, 'binary');

          // Add some spacing after field name
          currentY += rowHeight;

          // Add the image with reasonable dimensions
          doc.image(imageBuffer, 50, currentY, {
            fit: [200, 100],
            align: 'center',
          });

          // Add extra space after image
          currentY += 100;
        } catch (error) {
          // If image loading fails, show error message
          doc
            .font('Helvetica')
            .fontSize(10)
            .fillColor('#f44336')
            .text(
              'Failed to load image',
              200,
              currentY + (rowHeight - doc.currentLineHeight()) / 2,
            );
          currentY += rowHeight;
        }
      } else {
        // Regular field handling
        currentY = addFieldRow(
          field.form_field_name + ':',
          field.field_type_value,
          50,
          currentY,
          rowHeight,
        );
      }
    }

    // Add footer
    const footerText = 'UniGuard Security System';
    const footerWidth = doc.widthOfString(footerText);
    doc
      .fontSize(10)
      .fillColor('#9e9e9e')
      .text(
        footerText,
        (doc.page.width - footerWidth) / 2,
        doc.page.height - 50,
      );

    // Finalize the PDF
    doc.end();

    return new Promise(resolve => {
      doc.on('end', () => {
        const buffer = Buffer.concat(buffers);
        resolve({
          buffer: buffer,
          filename: `form-log-${dayjs().tz(formLog.timezone?.timezone_name).format('YYYYMMDD-HHmmss')}.pdf`,
        });
      });
    });
  }

  async generateSpreadsheetById(formLog: LogForm, timezone: Timezone): Promise<{
    buffer: Buffer;
    filename: string;
  }> {
    let currentRow = 1;
    // Create a new workbook and worksheet
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Form Log');

    // Add title and styling
    worksheet.mergeCells('A1:G1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = 'UNIGUARD FORM LOG REPORT';
    titleCell.font = { size: 16, bold: true, color: { argb: '1A237E' } };
    titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getRow(1).height = 30;
    currentRow++;

    // Add generation time
    worksheet.mergeCells('A2:G2');
    const timeCell = worksheet.getCell('A2');
    timeCell.value = `Generated: ${dayjs().tz(timezone.timezone_name).format('YYYY-MM-DD HH:mm')}`;
    timeCell.font = { size: 10, color: { argb: '546E7A' } };
    timeCell.alignment = { horizontal: 'center' };
    currentRow++;

    // Add UUID
    worksheet.mergeCells('A3:G3');
    const uuidCell = worksheet.getCell('A3');
    uuidCell.value = `UUID: ${formLog.uuid}`;
    uuidCell.font = { size: 10, color: { argb: '546E7A' } };
    currentRow++;

    // Add header row for basic info
    const headerRow = worksheet.addRow(['Field', 'Value']);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'E3F2FD' },
    };
    currentRow++;

    // Add data rows for basic info
    const dataRows = [
      ['Role', formLog.role_name || '-'],
      ['User', formLog.user_name || '-'],
      ['Form', formLog.form_name || '-'],
      ['Device', formLog.device_name || '-'],
      ['Timezone', formLog.timezone_name || '-'],
      ['Latitude', formLog.latitude?.toString() || '-'],
      ['Longitude', formLog.longitude?.toString() || '-'],
      [
        'Original Time',
        dayjs(formLog.original_submitted_time).tz(formLog.timezone_name).format('YYYY-MM-DD HH:mm'),
      ],
    ];

    // Add data rows with alternating colors
    dataRows.forEach((row, index) => {
      currentRow++;
      const excelRow = worksheet.addRow(row);
      // Add alternating row colors for better readability
      if (index % 2 === 1) {
        excelRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FAFAFA' },
        };
      }
    });

    // Add form fields section
    currentRow += 2;
    const formFieldsHeaderRow = worksheet.addRow(['Form Fields']);
    formFieldsHeaderRow.font = {
      size: 14,
      bold: true,
      color: { argb: '1A237E' },
    };
    currentRow++;

    // Add form fields header
    const formFieldsTableHeader = worksheet.addRow([
      'Field Name',
      'Field Type',
      'Value',
    ]);
    formFieldsTableHeader.font = { bold: true };
    formFieldsTableHeader.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'E3F2FD' },
    };
    currentRow++;

    // Get form fields and add them to the spreadsheet
    const formFields = await this.formFieldRepository.find({
      where: { log_form_id: formLog.id },
      relations: ['field_type'],
    });

    formFields.forEach((field, index) => {
      const fieldRow = worksheet.addRow([
        field.form_field_name,
        field.field_type_name,
        field.field_type_name === 'image' ||
        field.field_type_name === 'signature'
          ? {
              text: `View ${field.field_type_name}`,
              hyperlink: field.field_type_value,
              tooltip: `Click to view ${field.field_type_name}`,
            }
          : field.field_type_value,
      ]);

      if (index % 2 === 1) {
        fieldRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FAFAFA' },
        };
      }

      // Add hyperlink styling for image/signature fields
      if (
        field.field_type_name === 'image' ||
        field.field_type_name === 'signature'
      ) {
        const valueCell = fieldRow.getCell(3); // Value column
        valueCell.font = {
          color: { argb: '0000FF' },
          underline: true,
        };
      }

      currentRow++;
    });

    // Set column widths for better layout
    worksheet.getColumn(1).width = 25;
    worksheet.getColumn(2).width = 20;
    worksheet.getColumn(3).width = 50;

    // Add footer with system information
    currentRow += 2;
    const footerRowIndex = currentRow;
    worksheet.mergeCells(`A${footerRowIndex}:G${footerRowIndex}`);
    const footerCell = worksheet.getCell(`A${footerRowIndex}`);
    footerCell.value = 'UniGuard Security System';
    footerCell.font = { size: 10, color: { argb: '9E9E9E' } };
    footerCell.alignment = { horizontal: 'center' };

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();

    return {
      buffer: buffer as Buffer,
      filename: `form-log-${formLog.id}-${dayjs().tz(formLog.timezone?.timezone_name).format('YYYYMMDD-HHmmss')}.xlsx`,
    };
  }

  async generatePDF(
    data: LogForm[],
    filters: FormsLogFiltersGenerateDocument,
    timezone: Timezone,
  ): Promise<{ buffer: Buffer; filename: string }> {
    // Create a new PDF document with custom options
    const doc = this.createPdfDoc();
    // Create a buffer to store the PDF
    const buffers: Buffer[] = [];
    doc.on('data', buffers.push.bind(buffers));

    // Helper function to create section headers
    const drawSectionHeader = (text: string, y: number) => {
      doc
        .fontSize(16)
        .font('Helvetica-Bold')
        .fillColor('#1a237e')
        .text(text, 50, y);

      doc
        .moveTo(50, y + 25)
        .lineTo(545, y + 25)
        .lineWidth(1)
        .strokeColor('#1a237e')
        .stroke();
    };

    // Helper function to add field rows with vertical centering
    const addFieldRow = (
      label: string,
      value: string,
      x: number,
      y: number,
      rowHeight: number = 25,
    ) => {
      const labelY = y + (rowHeight - doc.currentLineHeight()) / 2;
      const valueY = y + (rowHeight - doc.currentLineHeight()) / 2;

      doc
        .font('Helvetica-Bold')
        .fontSize(10)
        .fillColor('#546e7a')
        .text(label, x, labelY);

      doc
        .font('Helvetica')
        .fontSize(10)
        .fillColor('#263238')
        .text(value || '-', x + 150, valueY);

      return y + rowHeight;
    };

    // Add cover page
    doc.addPage();

    // Add background
    doc.rect(0, 0, doc.page.width, doc.page.height).fill('#f5f5f5');

    // Add company logo and name
    try {
      const response = await axios.get(
        'https://trial-serverless.web.app/logo.png',
        {
          responseType: 'arraybuffer',
        },
      );
      const logoBuffer = Buffer.from(response.data, 'binary');

      // Add Logo
      doc.image(logoBuffer, (doc.page.width - 80) / 2, 100, {
        fit: [80, 80],
      });
    } catch (error) {
      // Fallback if logo loading fails
      doc
        .save()
        .translate(doc.page.width / 2, 140)
        .rect(-40, -40, 80, 80)
        .fill('#1a237e');
    }

    // Add company name
    doc
      .font('Helvetica-Bold')
      .fontSize(18)
      .fillColor('#1a237e')
      .text(
        'UNIGUARD',
        (doc.page.width - doc.widthOfString('UNIGUARD')) / 2,
        190,
      );

    // Add title
    doc.font('Helvetica-Bold').fontSize(18).fillColor('#1a237e');

    const titleWidth = doc.widthOfString('FORM LOG REPORT');
    doc.text('FORM LOG REPORT', (doc.page.width - titleWidth) / 2, 240);

    // Moved metadata section up (from 350 to 300)
    const metadataY = 300;
    doc.font('Helvetica').fontSize(12).fillColor('#546e7a');

    // Add active filters section
    doc
      .font('Helvetica-Bold')
      .fontSize(14)
      .fillColor('#1a237e')
      .text('Active Filters', 50, metadataY);

    doc
      .moveTo(50, metadataY + 25)
      .lineTo(545, metadataY + 25)
      .lineWidth(1)
      .strokeColor('#1a237e')
      .stroke();

    let filterY = metadataY + 30;

    // Display active filters in a clean table format
    const leftFilters = [
      {
        key: 'startDate',
        label: 'Start Date:',
        valueFunc: () =>
          filters.startDate
            ? `${dayjs(filters.startDate).format('MMMM D, YYYY')}`
            : 'All Dates',
      },
      {
        key: 'endDate',
        label: 'End Date:',
        valueFunc: () =>
          filters.endDate
            ? `${dayjs(filters.endDate).format('MMMM D, YYYY')}`
            : 'All Dates',
      },
      {
        key: 'user',
        label: 'User:',
        valueFunc: () => (filters.user ? `${filters.user.name}` : 'All Users'),
      },
      {
        key: 'device',
        label: 'Device:',
        valueFunc: () =>
          filters.device ? `${filters.device.device_name}` : 'All Devices',
      },
      {
        key: 'form',
        label: 'Form:',
        valueFunc: () =>
          filters.form ? `${filters.form.form_name}` : 'All Forms',
      },
    ];

    const rightFilters = [
      {
        key: 'startTime',
        label: 'Start Time:',
        valueFunc: () =>
          filters.startTime ? `${filters.startTime}` : 'All Times',
      },
      {
        key: 'endTime',
        label: 'End Time:',
        valueFunc: () => (filters.endTime ? `${filters.endTime}` : 'All Times'),
      },
      {
        key: 'role',
        label: 'Role:',
        valueFunc: () =>
          filters.role ? `${filters.role.role_name}` : 'All Roles',
      },
      {
        key: 'user_labels',
        label: 'User Labels:',
        valueFunc: () =>
          filters.user_labels && filters.user_labels.length > 0
            ? filters.user_labels.map(label => label.label_name).join(', ')
            : 'All Labels',
      },
      {
        key: 'device_labels',
        label: 'Device Labels:',
        valueFunc: () =>
          filters.device_labels && filters.device_labels.length > 0
            ? filters.device_labels.map(label => label.label_name).join(', ')
            : 'All Labels',
      },
    ];

    // Render filters in two columns
    const rowHeight = 25;
    const leftX = 50;
    const rightX = 300;

    const maxRows = Math.max(leftFilters.length, rightFilters.length);
    for (let i = 0; i < maxRows; i++) {
      // Add left column filter
      if (i < leftFilters.length) {
        const leftFilter = leftFilters[i];
        addFieldRow(
          leftFilter.label,
          leftFilter.valueFunc(),
          leftX,
          filterY,
          rowHeight,
        );
      }

      // Add right column filter
      if (i < rightFilters.length) {
        const rightFilter = rightFilters[i];
        addFieldRow(
          rightFilter.label,
          rightFilter.valueFunc(),
          rightX,
          filterY,
          rowHeight,
        );
      }

      filterY += rowHeight;
    }

    // Add generation info
    doc.font('Helvetica').fontSize(12).fillColor('#546e7a');

    filterY = addFieldRow(
      'Report Generated:',
      dayjs().tz(timezone.timezone_name).format('YYYY-MM-DD HH:mm'),
      50,
      filterY,
    );
    filterY = addFieldRow(
      'Total Entries:',
      data.length.toString(),
      50,
      filterY,
    );

    // Add date range explanation section
    if (filters.startDate && filters.endDate) {
      filterY += 20;
      doc
        .font('Helvetica-Bold')
        .fontSize(14)
        .fillColor('#1a237e')
        .text('Data From:', 50, filterY);

      filterY += 25;

      // Calculate all days between start and end date
      const startDate = dayjs(filters.startDate);
      const endDate = dayjs(filters.endDate);
      const daysDiff = endDate.diff(startDate, 'day') + 1;
      const startTime = filters.startTime || '00:00';
      const endTime = filters.endTime || '23:59';

      // Display up to 5 days with truncation if more
      const maxDaysToShow = 3;
      const daysToShow = Math.min(daysDiff, maxDaysToShow);

      for (let i = 0; i < daysToShow; i++) {
        const currentDate = startDate.add(i, 'day');
        const dateStr = currentDate.format('YYYY-MM-DD');
        const timeRangeStr = `${dateStr} ${startTime} until ${dateStr} ${endTime}`;

        doc
          .font('Helvetica')
          .fontSize(10)
          .fillColor('#263238')
          .text(timeRangeStr, 50, filterY + i * 20);
      }

      // Add truncation indicator if there are more days
      if (daysDiff > maxDaysToShow) {
        doc
          .font('Helvetica')
          .fontSize(10)
          .fillColor('#263238')
          .text('...', 50, filterY + maxDaysToShow * 20);

        // Show the last day
        const lastDate = endDate.format('YYYY-MM-DD');
        const lastTimeRangeStr = `${lastDate} ${startTime} until ${lastDate} ${endTime}`;

        doc
          .font('Helvetica')
          .fontSize(10)
          .fillColor('#263238')
          .text(lastTimeRangeStr, 50, filterY + (maxDaysToShow + 1) * 20);

        filterY += (maxDaysToShow + 2) * 20;
      } else {
        filterY += daysToShow * 20;
      }
    }

    // Add footer to cover page
    const footerText = 'UniGuard Security System';
    const footerWidth = doc.widthOfString(footerText);
    doc
      .fontSize(10)
      .fillColor('#9e9e9e')
      .text(
        footerText,
        (doc.page.width - footerWidth) / 2,
        doc.page.height - 100,
      );

    // Process each form log entry
    for (const [index, log] of data.entries()) {
      doc.addPage();

      // Add page header with UUID
      drawSectionHeader(`Form Log Entry #${index + 1}`, 50);

      // Add UUID right after header
      doc
        .font('Helvetica')
        .fontSize(10)
        .fillColor('#546e7a')
        .text(`UUID: ${log.uuid}`, 50, 85);

      // Add horizontal line after UUID
      doc
        .moveTo(50, 100)
        .lineTo(545, 100)
        .lineWidth(0.5)
        .strokeColor('#e0e0e0')
        .stroke();

      // Add entry details with improved layout
      let currentY = 120;

      // Create a table-like structure for the data
      const leftColumnData = [
        { label: 'Role', value: log.role?.role_name },
        { label: 'User', value: log.user?.name },
        { label: 'Form', value: log.form?.form_name },
        { label: 'Device', value: log.device?.device_name },
      ];

      const rightColumnData = [
        { label: 'Timezone', value: log.timezone?.timezone_name },
        { label: 'Latitude', value: log.latitude?.toString() },
        { label: 'Longitude', value: log.longitude?.toString() },
        {
          label: 'Original Time',
          value: dayjs(log.original_submitted_time).tz(log.timezone_name).format(
            'YYYY-MM-DD HH:mm',
          ),
        },
      ];

      // Draw table content with alternating background and vertical centering
      leftColumnData.forEach((item, i) => {
        const rowHeight = 30; // Increased row height for better spacing

        // Draw alternating background
        if (i % 2 === 0) {
          doc.fillColor('#fafafa').rect(50, currentY, 495, rowHeight).fill();
        }

        // Draw left column data
        currentY = addFieldRow(
          item.label + ':',
          item.value,
          50,
          currentY,
          rowHeight,
        );

        // Draw right column data if available
        if (i < rightColumnData.length) {
          addFieldRow(
            rightColumnData[i].label + ':',
            rightColumnData[i].value,
            300,
            currentY - rowHeight,
            rowHeight,
          );
        }
      });

      // Add form fields section
      currentY += 20;
      drawSectionHeader('Form Fields', currentY);
      currentY += 40;

      // Get form fields and add them to the PDF
      const formFields = await this.formFieldRepository.find({
        where: { log_form_id: log.id },
        relations: ['field_type'],
      });

      for (const [fieldIndex, field] of formFields.entries()) {
        const rowHeight = 30;
        if (fieldIndex % 2 === 0) {
          doc.fillColor('#fafafa').rect(50, currentY, 495, rowHeight).fill();
        }

        // Handle image and signature fields differently
        if (
          field.field_type_name === 'image' ||
          field.field_type_name === 'signature'
        ) {
          // Add field name
          doc
            .font('Helvetica-Bold')
            .fontSize(10)
            .fillColor('#546e7a')
            .text(
              field.form_field_name + ':',
              50,
              currentY + (rowHeight - doc.currentLineHeight()) / 2,
            );

          try {
            // Try to load and add the image
            const response = await axios.get(field.field_type_value, {
              responseType: 'arraybuffer',
            });
            const imageBuffer = Buffer.from(response.data, 'binary');

            // Add some spacing after field name
            currentY += rowHeight;

            // Add the image with reasonable dimensions
            doc.image(imageBuffer, 50, currentY, {
              fit: [200, 100],
              align: 'center',
            });

            // Add extra space after image
            currentY += 100;
          } catch (error) {
            // If image loading fails, show error message
            doc
              .font('Helvetica')
              .fontSize(10)
              .fillColor('#f44336')
              .text(
                'Failed to load image',
                200,
                currentY + (rowHeight - doc.currentLineHeight()) / 2,
              );
            currentY += rowHeight;
          }
        } else {
          // Regular field handling
          currentY = addFieldRow(
            field.form_field_name + ':',
            field.field_type_value,
            50,
            currentY,
            rowHeight,
          );
        }
      }

      // Add page number at fixed position from bottom
      const pageText = `Page ${index + 2} of ${data.length + 1}`;
      const pageWidth = doc.widthOfString(pageText);
      doc
        .font('Helvetica')
        .fontSize(10)
        .fillColor('#9e9e9e')
        .text(pageText, (doc.page.width - pageWidth) / 2, doc.page.height - 30);
    }

    // Finalize the PDF
    doc.end();

    return new Promise(resolve => {
      doc.on('end', () => {
        const buffer = Buffer.concat(buffers);
        resolve({
          buffer: buffer,
          filename: `form-logs-${dayjs().tz(data.length > 0 ? data[0].timezone?.timezone_name : timezone.timezone_name).format('YYYYMMDD-HHmmss')}.pdf`,
        });
      });
    });
  }

  async generateSpreadsheet(
    data: LogForm[],
    filters: FormsLogFiltersGenerateDocument,
    timezone: Timezone,
  ): Promise<{ buffer: Buffer; filename: string }> {
    // Create a new workbook and worksheet
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Form Logs');
    let currentRow = 1;

    // Add title and styling (merged across all columns)
    worksheet.mergeCells('A1:K1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = 'UNIGUARD FORM LOGS REPORT';
    titleCell.font = { size: 16, bold: true, color: { argb: '1A237E' } };
    titleCell.alignment = { horizontal: 'center', vertical: 'middle' };

    // Add generation time and total entries
    currentRow++;
    worksheet.mergeCells(`A${currentRow}:K${currentRow}`);
    const timeCell = worksheet.getCell(`A${currentRow}`);
    timeCell.value = `Generated: ${dayjs().tz(timezone.timezone_name).format('YYYY-MM-DD HH:mm')} | Total Entries: ${data.length}`;
    timeCell.font = { size: 10, color: { argb: '546E7A' } };
    timeCell.alignment = { horizontal: 'center' };

    // Add filter information
    currentRow++;
    worksheet.mergeCells(`A${currentRow}:K${currentRow}`);
    const filterTitleCell = worksheet.getCell(`A${currentRow}`);
    filterTitleCell.value = 'FILTER CRITERIA';
    filterTitleCell.font = { size: 12, bold: true, color: { argb: '1A237E' } };

    // Add filter details
    if (filters.startDate && filters.endDate) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Date Range:';
      worksheet.getCell(`B${currentRow}`).value =
        `${filters.startDate} to ${filters.endDate}`;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.startTime && filters.endTime) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Time Range:';
      worksheet.getCell(`B${currentRow}`).value =
        `${filters.startTime} to ${filters.endTime}`;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.role) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Role:';
      worksheet.getCell(`B${currentRow}`).value = filters.role.role_name;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.user) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'User:';
      worksheet.getCell(`B${currentRow}`).value = filters.user.name;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    } else if (filters.user_labels.length > 0) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'User Labels:';
      worksheet.getCell(`B${currentRow}`).value = filters.user_labels
        .map(label => label.label_name)
        .join(', ');
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.device) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Device:';
      worksheet.getCell(`B${currentRow}`).value = filters.device.device_name;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    } else if (filters.device_labels.length > 0) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Device Labels:';
      worksheet.getCell(`B${currentRow}`).value = filters.device_labels
        .map(label => label.label_name)
        .join(', ');
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.form) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Form:';
      worksheet.getCell(`B${currentRow}`).value = filters.form.form_name;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    // Add date range explanation section
    if (filters.startDate && filters.endDate) {
      currentRow += 2;
      let startRow = currentRow;
      let endRow = currentRow;
      const cellDateRange = worksheet.getCell(`A${currentRow}`);
      cellDateRange.value = 'Date Range:';
      cellDateRange.font = { bold: true };
      cellDateRange.alignment = { horizontal: 'center', vertical: 'middle' };

      // Calculate the number of days between the start and end date
      const startDate = dayjs(filters.startDate);
      const endDate = dayjs(filters.endDate);
      const daysDiff = endDate.diff(startDate, 'day') + 1;
      const startTime = filters.startTime || '00:00';
      const endTime = filters.endTime || '23:59';

      const maxDaysToShow = 3;
      const daysToShow = Math.min(daysDiff, maxDaysToShow);

      for (let i = 0; i < daysToShow; i++) {
        if (i > 0) {
          currentRow++;
        }
        endRow = currentRow;

        const currentDate = startDate.add(i, 'day');
        const dateStr = currentDate.format('YYYY-MM-DD');
        worksheet.getCell(`B${currentRow}`).value =
          `${dateStr} ${startTime} until ${dateStr} ${endTime}`;
      }

      if (daysDiff > maxDaysToShow) {
        currentRow++;
        worksheet.getCell(`B${currentRow}`).value = '...';

        // Show last day
        currentRow++;
        endRow = currentRow;
        const dateStr = endDate.format('YYYY-MM-DD');
        worksheet.getCell(`B${currentRow}`).value =
          `${dateStr} ${startTime} until ${dateStr} ${endTime}`;
      }

      // Merge the date range cells
      worksheet.mergeCells(`A${startRow}:A${endRow}`);
    }

    // Add main data section header
    currentRow += 2;
    const mainDataHeaderRow = worksheet.addRow(['Form Logs Data']);
    mainDataHeaderRow.font = {
      size: 14,
      bold: true,
      color: { argb: '1A237E' },
    };
    currentRow++;

    // Add table headers for basic info
    const headers = [
      'UUID',
      'Role',
      'User',
      'Form',
      'Device',
      'Timezone',
      'Latitude',
      'Longitude',
      'Original Time',
    ];

    // Add header row with styling
    const headerRow = worksheet.addRow(headers);
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '1A237E' },
    };
    headerRow.alignment = { horizontal: 'center' };
    currentRow++;

    // Process each form log
    for (const [index, log] of data.entries()) {
      // Add basic info row
      const basicInfoRow = worksheet.addRow([
        log.uuid,
        log.role?.role_name || '-',
        log.user?.name || '-',
        log.form?.form_name || '-',
        log.device?.device_name || '-',
        log.timezone?.timezone_name || '-',
        log.latitude?.toString() || '-',
        log.longitude?.toString() || '-',
        dayjs(log.original_submitted_time).tz(log.timezone_name).format('YYYY-MM-DD HH:mm'),
      ]);

      // Add alternating colors for better readability
      if (currentRow % 2 === 0) {
        basicInfoRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'F5F5F5' },
        };
      }
      currentRow++;

      // Get form fields for this log
      const formFields = await this.formFieldRepository.find({
        where: { log_form_id: log.id },
        relations: ['field_type'],
      });

      // If there are form fields, add them in a nested format
      if (formFields.length > 0) {
        // Add form fields header
        const fieldHeaders = ['Field Name', 'Field Type', 'Value'];
        const fieldHeaderRow = worksheet.addRow(['Form Fields:', '', '']);
        fieldHeaderRow.font = { bold: true, color: { argb: '1A237E' } };
        currentRow++;

        const fieldTableHeader = worksheet.addRow(fieldHeaders);
        fieldTableHeader.font = { bold: true };
        fieldTableHeader.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'E3F2FD' },
        };
        currentRow++;

        // Add form fields
        formFields.forEach((field, fieldIndex) => {
          const fieldRow = worksheet.addRow([
            field.form_field_name,
            field.field_type_name,
            field.field_type_name === 'image' ||
            field.field_type_name === 'signature'
              ? {
                  text: `View ${field.field_type_name}`,
                  hyperlink: field.field_type_value,
                  tooltip: `Click to view ${field.field_type_name}`,
                }
              : field.field_type_value,
          ]);

          if (fieldIndex % 2 === 1) {
            fieldRow.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'FAFAFA' },
            };
          }

          // Add hyperlink styling for image/signature fields
          if (
            field.field_type_name === 'image' ||
            field.field_type_name === 'signature'
          ) {
            const valueCell = fieldRow.getCell(3); // Value column
            valueCell.font = {
              color: { argb: '0000FF' },
              underline: true,
            };
          }

          currentRow++;
        });

        // Add a blank row after form fields
        worksheet.addRow([]);
        currentRow++;
      }

      // Add separator line between form logs if not the last entry
      if (index < data.length - 1) {
        // Add a blank row
        worksheet.addRow([]);
        currentRow++;

        // Add separator line
        const separatorRow = worksheet.addRow(['']);
        for (let i = 1; i <= headers.length; i++) {
          const cell = separatorRow.getCell(i);
          cell.border = {
            bottom: { style: 'thin', color: { argb: 'E0E0E0' } },
          };
        }
        currentRow++;

        // Add another blank row for spacing
        worksheet.addRow([]);
        currentRow++;
      }
    }

    // Set column widths for better layout
    worksheet.getColumn(1).width = 36; // UUID
    worksheet.getColumn(2).width = 20; // Role
    worksheet.getColumn(3).width = 20; // User
    worksheet.getColumn(4).width = 20; // Form
    worksheet.getColumn(5).width = 20; // Device
    worksheet.getColumn(6).width = 20; // Timezone
    worksheet.getColumn(7).width = 15; // Latitude
    worksheet.getColumn(8).width = 15; // Longitude
    worksheet.getColumn(9).width = 20; // Original Time

    // Add footer
    currentRow += 2;
    const footerRowIndex = currentRow;
    worksheet.mergeCells(`A${footerRowIndex}:K${footerRowIndex}`);
    const footerCell = worksheet.getCell(`A${footerRowIndex}`);
    footerCell.value = 'UniGuard Security System';
    footerCell.font = { size: 10, color: { argb: '9E9E9E' } };
    footerCell.alignment = { horizontal: 'center' };

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();

    return {
      buffer: buffer as Buffer,
      filename: `form-logs-${dayjs().tz(data.length > 0 ? data[0].timezone?.timezone_name : timezone.timezone_name).format('YYYYMMDD-HHmmss')}.xlsx`,
    };
  }
}
