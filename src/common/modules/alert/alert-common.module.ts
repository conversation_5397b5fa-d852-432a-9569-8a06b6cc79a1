import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AlertCommonService } from './services/alert-common.service';
import { Alert } from '../database/entities/alert.entity';
import { Device } from '../database/entities/device.entity';
import { UserBranch } from '../database/entities/user-branch.entity';
import { SendgridModule } from '../mailer/sendgrid/sendgrid.module';
import { ActivityAnalyticModule } from '../analytics/activity/activity-analytic.module';
import { TaskAnalyticModule } from '../analytics/task/task-analytic.module';
import { FormAnalyticModule } from '../analytics/form/form-analytic.module';
import { AlarmAnalyticModule } from '../analytics/alarm/alarm-analytic.module';
import { CheckpointActivityAnalyticModule } from '../analytics/checkpoint-activity/checkpoint-activity-analytic.module';
import { SignInOutAnalyticModule } from '../analytics/sign-in-out/sign-in-out-analytic.module';

@Module({
  imports: [
    SendgridModule,
    ActivityAnalyticModule,
    TaskAnalyticModule,
    FormAnalyticModule,
    AlarmAnalyticModule,
    CheckpointActivityAnalyticModule,
    SignInOutAnalyticModule,
  ],
  providers: [AlertCommonService],
  exports: [AlertCommonService],
})
export class AlertCommonModule {}
