import { LogActivity } from '../../database/entities/log-activity.entity';
import { LogForm } from '../../database/entities/log-form.entity';
import { LogTask } from '../../database/entities/log-task.entity';
import { LogAlarm } from '../../database/entities/log-alarm.entity';
import { LogCheckpoint } from '../../database/entities/log-checkpoint.entity';
import { LogUserDevice } from '../../database/entities/log-user-device.entity';

export interface IAlertParameters {
  userId: number;
  roleId: number;
  submittedDateTime: string;
  checkpointId?: number;
  geofenceId?: number;
  zoneId?: number;
  deviceId: number;
  parentBranchId: number;

  alertEventId: number;
  logActivity?: LogActivity | null;
  logTask?: LogTask | null;
  logForm?: LogForm | null;
  logAlarm?: LogAlarm | null;
  logCheckpoint?: LogCheckpoint | null;
  logSignInOut?: LogUserDevice | null;
}
